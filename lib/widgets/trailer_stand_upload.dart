import 'dart:io';

import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:spot_on/providers/bol_upload_state.dart';
import 'package:spot_on/providers/trailer_stand_upload_state.dart';
import 'package:spot_on/utils/gaps.dart';
import 'package:spot_on/utils/imports.dart';

class TrailerStandUpload extends StatefulWidget {
  const TrailerStandUpload({
    Key? key,
    required this.reason,
    required this.jobId,
  }) : super(key: key);

  final String? reason;
  final String jobId;

  @override
  State<TrailerStandUpload> createState() => _TrailerStandUploadState();
}

class _TrailerStandUploadState extends State<TrailerStandUpload> {
  final ImagePicker _picker = ImagePicker();
  final List<XFile> _imageList = [];
  bool isLoading = false;

  bool confirmClose = false;

  Future<void> showLoading() async {
    setState(() {
      isLoading = true;
    });
    await Future.delayed(const Duration(seconds: 2));
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final trailerStandUploadState = context.watch<TrailerStandUploadState>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (trailerStandUploadState.navigateBack) {
        trailerStandUploadState.reset();
        Navigator.of(context).pop();
      }
    });
    return WillPopScope(
      onWillPop: () {
        return Future.value(false);
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: Colors.white,
          leadingWidth: 4,
          elevation: 0.5,
          title: Text(
            // '${widget.isUnsigned ? "New" : "Completed"} BoL',
            '',
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          leading: null,
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.white,
            statusBarIconBrightness: Brightness.dark,
          ),
        ),
        body: ModalProgressHUD(
          inAsyncCall: trailerStandUploadState.loading || isLoading,
          opacity: 0,
          color: Colors.white,
          blur: 0.3,
          child: Container(
            height: MediaQuery.of(context).size.height,
            padding: const EdgeInsets.all(20),
            child: ListView(
              // mainAxisSize: MainAxisSize.min,
              // physics: NeverScrollableScrollPhysics(),
              children: [
                const Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'Upload Image',
                    style: TextStyle(
                      fontSize: 16,
                      color: appBg,
                      // fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                gapH24,
                ListView.separated(
                  shrinkWrap: true,
                  itemCount: _imageList.length,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    return Container(
                      width: double.infinity,
                      height: 64,
                      decoration: BoxDecoration(
                          color: const Color(0xFFF1F1F1),
                          borderRadius: BorderRadius.circular(12)),
                      child: Row(
                        children: [
                          GestureDetector(
                            onTap: () {
                              showImageViewer(
                                context,
                                Image.file(
                                  File(_imageList[index].path),
                                  fit: BoxFit.cover,
                                ).image,
                                swipeDismissible: true,
                                doubleTapZoomable: true,
                                backgroundColor: appColor,
                              );
                            },
                            child: Container(
                              margin: const EdgeInsets.all(4),
                              height: double.infinity,
                              width: 56,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                color: appColor,
                              ),
                              clipBehavior: Clip.hardEdge,
                              child: Image.file(
                                File(_imageList[index].path),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          gapW8,
                          Expanded(
                            child: Text(
                              "Image ${index + 1}",
                              style: const TextStyle(color: appBg),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          gapW12,
                          Container(
                            height: double.infinity,
                            width: 64,
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topRight: Radius.circular(12),
                                bottomRight: Radius.circular(12),
                              ),
                              color: Color(0XFFE8E8E8),
                            ),
                            child: IconButton(
                              onPressed: () {
                                setState(() {
                                  _imageList.removeAt(index);
                                });
                              },
                              icon: const Icon(
                                Icons.close,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return gapH12;
                  },
                ),
                gapH24,
                Row(
                  children: [
                    Expanded(
                      child: Material(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.white,
                        clipBehavior: Clip.hardEdge,
                        child: InkWell(
                          onTap: () async {
                            try {
                              final XFile? image = await _picker.pickImage(
                                source: ImageSource.camera,
                                requestFullMetadata: false,
                                imageQuality: 25,
                              );
                              showLoading();
                              setState(() {
                                if (image != null) {
                                  _imageList.add(image);
                                  print(_imageList.length);
                                }
                              });
                            } catch (e) {
                              showToast("Error occurred!");
                            }
                          },
                          child: Container(
                            height: 56,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.all(8),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                gapW8,
                                Icon(Icons.camera_alt_outlined),
                                gapW8,
                                Expanded(
                                  child: Text(
                                    'Click a photo',
                                    style: TextStyle(fontSize: 12),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    gapW8,
                    Expanded(
                      child: Material(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.white,
                        clipBehavior: Clip.hardEdge,
                        child: InkWell(
                          onTap: () async {
                            try {
                              final XFile? image = await _picker.pickImage(
                                source: ImageSource.gallery,
                                requestFullMetadata: false,
                                imageQuality: 25,
                              );
                              showLoading();
                              setState(() {
                                if (image != null) {
                                  _imageList.add(image);
                                  print(_imageList.length);
                                }
                              });
                            } catch (e) {
                              showToast("Error occurred!");
                            }
                          },
                          child: Container(
                            height: 56,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.all(8),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                gapW8,
                                Icon(Icons.image_outlined),
                                gapW8,
                                Expanded(
                                  child: Text(
                                    'Select from gallery',
                                    style: TextStyle(fontSize: 12),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                gapH32,
                SizedBox(
                  width: double.infinity,
                  height: 40,
                  child: ElevatedButton(
                    onPressed: () async {
                      if (_imageList.isEmpty) {
                        showToast("No image selected!");
                        return;
                      }
                      trailerStandUploadState.addImages(_imageList);
                      if (widget.reason != null || widget.reason!.isNotEmpty) {
                        trailerStandUploadState.uploadImages(
                          images: _imageList,
                          jobId: widget.jobId,
                          reason: widget.reason.toString(),
                        );
                      }
                      else{
                          trailerStandUploadState.uploadImageswithoutReason(
                          images: _imageList,
                          jobId: widget.jobId,
                          reason: widget.reason.toString(),
                        );
                      }

                      // Handle the submit button action
                      // Navigator.of(context).pop(); // Close the dialog
                    },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(32), // Rounded corners
                      ),
                    ),
                    child: const Text(
                      'UPLOAD',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                gapH16,
                SizedBox(
                  width: double.infinity,
                  height: 40,
                  child: ElevatedButton(
                    onPressed: () async {
                      trailerStandUploadState.reset();
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(32), // Rounded corners
                      ),
                      backgroundColor: Colors.red.shade800,
                    ),
                    child: const Text(
                      'CANCEL',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
