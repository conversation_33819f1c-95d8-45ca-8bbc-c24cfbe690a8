import 'package:flutter/material.dart';
import 'package:spot_on/utils/constants.dart';
import 'package:spot_on/widgets/app_logo.dart';

class AppLogoHeader extends StatelessWidget {
  const AppLogoHeader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height / 3,
      child: Stack(
        children: [
          Image.asset(
            loginBanner,
            filterQuality: FilterQuality.high,
            fit: BoxFit.fill,
            width: MediaQuery.of(context).size.width,
          ),
          const Align(
            child: SizedBox(
              width: 200,
              height: 100,
              child: AppLogo1(),
            ),
          ),
        ],
      ),
    );
  }
}
