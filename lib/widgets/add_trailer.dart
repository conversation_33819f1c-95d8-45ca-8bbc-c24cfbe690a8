// import 'package:spot_on/models/addnew/carrieradd.dart';
import 'package:spot_on/providers/new_trailer_state.dart';
import 'package:spot_on/screens/add_new_trailer.dart';

import '../utils/imports.dart';

class AddTrailer extends StatelessWidget {
  const AddTrailer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        InkWell(
          onTap: () async {
            NewTrailerState newTrailerState = context.read<NewTrailerState>();
            newTrailerState.setDefaultTrailerType();
            newTrailerState.loadClients();
            openAddNewTruckScreen();
          },
          child: const AppTxt(
            text: 'Add Trailer Manually',
            color: appBg,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}

class AddTrailer1 extends StatelessWidget {
  final Function(dynamic)? onTrailerAdded; // Callback to pass `val` back

  const AddTrailer1({Key? key, this.onTrailerAdded}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        InkWell(
          onTap: () async {
            NewTrailerState newTrailerState = context.read<NewTrailerState>();
            newTrailerState.setDefaultTrailerType();
            newTrailerState.loadClients();
            // openAddNewTruckScreen();

            Navigator.push(
              globalKey.currentContext!,
              MaterialPageRoute(builder: (_) => const AddNewTrailer()),
            ).then((val) {
              if (onTrailerAdded != null) {
                onTrailerAdded!(val); // Pass `val` back to the parent
              }
            });
            //   Navigator.push(globalKey.currentContext!,
            //       MaterialPageRoute(builder: (_) => const AddNewTrailer()));
          },
          child: const AppTxt(
            text: 'Add Trailer Manually',
            color: appBg,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}

class AddCarrier extends StatelessWidget {
  final Function(dynamic)? onCarrierAdded; // Callback to pass `val` back

  const AddCarrier({Key? key, this.onCarrierAdded}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        InkWell(
          onTap: () async {
            Navigator.push(
              globalKey.currentContext!,
              MaterialPageRoute(builder: (_) => const AddNewCarrier()),
            ).then((val) {
              if (onCarrierAdded != null) {
                onCarrierAdded!(val); // Pass `val` back to the parent
              }
            });
          },
          child: const AppTxt(
            text: 'Add Carrier Manually',
            color: appBg,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}

class AddSupplier extends StatelessWidget {
  final Function(dynamic)? onSupplierAdded; // Callback to pass `val` back

  const AddSupplier({Key? key, this.onSupplierAdded}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        InkWell(
          onTap: () async {
            // NewTrailerState newTrailerState = context.read<NewTrailerState>();
            // newTrailerState.setDefaultTrailerType();
            // newTrailerState.loadClients();
            // openAddNewSupplierScreen();
            Navigator.push(
              globalKey.currentContext!,
              MaterialPageRoute(builder: (_) => const AddNewSupplier()),
            ).then((val) {
              if (onSupplierAdded != null) {
                onSupplierAdded!(val); // Pass `val` back to the parent
              }
            });
          },
          child: const AppTxt(
            text: 'Add Supplier Manually',
            color: appBg,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}
