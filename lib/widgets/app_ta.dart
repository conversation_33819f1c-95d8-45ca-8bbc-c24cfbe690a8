import 'dart:io';

import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:material_color_gen/material_color_gen.dart';
import 'package:spot_on/utils/imports.dart';

class AppTA extends StatelessWidget {
  //
  const AppTA({
    Key? key,
    this.minLines = 6,
    this.hintText = '',
    this.labelText = '',
    required this.onTextChange,
  }) : super(key: key);

  final String labelText;
  final String hintText;
  final int minLines;
  final Function(String) onTextChange;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      minLines: minLines,
      keyboardType: TextInputType.multiline,
      maxLines: null,
      decoration: InputDecoration(
        labelText: labelText,
        isDense: true,
        fillColor: Colors.white,
        labelStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
            height: 0.2),
        hintText: hintText,
        hintStyle: TextStyle(color: appGrey, fontSize: 18),
        border: UnderlineInputBorder(
          borderSide: BorderSide(color: appGrey),
        ),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: appGrey.withOpacity(0.3)),
        ),
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: appGrey),
        ),
        alignLabelWithHint: true,
      ),
      onChanged: (val) async {
        onTextChange(val);
      },
    );
  }
}

class AppTA1 extends StatefulWidget {
  const AppTA1({
    Key? key,
    required this.controller,
    this.minLines = 6,
    this.hintText = '',
    this.labelText = '',
    required this.onTextChange,
    // required this.onSubmit, // Add onSubmit callback
  }) : super(key: key);

  final TextEditingController controller; // Declare the controller variable
  final String labelText;
  final String hintText;
  final int minLines;
  final Function(String) onTextChange;
  // final Function() onSubmit; // Callback for submit action

  @override
  _AppTA1State createState() => _AppTA1State();
}

class _AppTA1State extends State<AppTA1> {
  FocusNode _focusNode = FocusNode(); // Create a FocusNode

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        // Dismiss keyboard if the focus is lost
        FocusScope.of(context).unfocus();
      }
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: widget.controller, // Use the passed controller
      minLines: widget.minLines,
      keyboardType: TextInputType.text, // Set to text to avoid new lines
      maxLines: null,
      inputFormatters: [
        // Prevents new line characters
        // FilteringTextInputFormatter.deny(RegExp(r'\n')),
      ],
      focusNode: _focusNode, // Assign the FocusNode
      decoration: InputDecoration(
        hintText: widget.hintText,
        hintStyle: const TextStyle(color: Colors.grey, fontSize: 12),
        border: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.grey),
        ),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
        ),
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.grey),
        ),
        alignLabelWithHint: true,
      ),
      onChanged: (val) {
        widget.onTextChange(val);
      },
      // onSubmitted: (val) {
      //   widget.onSubmit(); // Call onSubmit when Enter is pressed
      // },
    );
  }
}

class AppTACalendar extends StatefulWidget {
  const AppTACalendar({
    Key? key,
    required this.controller,
    this.minLines = 6,
    this.hintText = '',
    this.labelText = '',
    required this.onTextChange,
  }) : super(key: key);

  final TextEditingController controller;
  final String labelText;
  final String hintText;
  final int minLines;
  final Function(String) onTextChange;

  @override
  _AppTACalendarState createState() => _AppTACalendarState();
}

class _AppTACalendarState extends State<AppTACalendar> {
  DateTime _selectedDate = DateTime.now();
  @override
  void dispose() {
    // Do not dispose the controller here, since it is passed from outside
    super.dispose();
  }
Future<void> _selectDate(BuildContext context) async {
    // Parse the current value from controller if it exists
    if (widget.controller.text.isNotEmpty) {
      _selectedDate = DateFormat('MM-dd-yyyy').parse(widget.controller.text);
    }

    DateTime? picked = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: Theme(
            data: Theme.of(context).copyWith(
              textTheme: TextTheme(
                bodyLarge: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.black,
                ),
                titleLarge: const TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
                bodyMedium: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: Colors.black,
                ),
              ),
              colorScheme: ColorScheme.fromSeed(
                seedColor: appBg,
                primary: appBg,
                onPrimary: Colors.white,
                secondary: Colors.blue.shade100,
                onSecondary: Colors.black,
              ),
            ),
            child: Container(
              padding: const EdgeInsets.all(16),
              child: CalendarDatePicker(
                initialDate: _selectedDate, // Use the stored selected date
                firstDate: DateTime(2000),
                lastDate: DateTime(2100),
                onDateChanged: (DateTime date) {
                  Navigator.of(context).pop(date);
                },
              ),
            ),
          ),
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked; // Update the stored selected date
      });
      String formattedDate = DateFormat('MM-dd-yyyy').format(picked);
      widget.controller.text = formattedDate;
      widget.onTextChange(formattedDate);
    }
  }


  // Future<void> _selectDate(BuildContext context) async {
  //   DateTime? picked = await showDatePicker(
  //     context: context,
  //     initialDate: DateTime.now(),
  //     firstDate: DateTime(2000),
  //     lastDate: DateTime(2100),
  //   );

  //   if (picked != null) {
  //     String formattedDate = DateFormat('yyyy-MM-dd').format(picked);
  //     widget.controller.text = formattedDate;
  //     widget.onTextChange(formattedDate);
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _selectDate(context),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
           Container(
             margin: const EdgeInsets.only(top: 12.0),
             child: const Icon(
               Icons.calendar_today,
               color: Colors.grey,
             ),
           ),
           SizedBox(width: 10,),
          Expanded(
            flex: 2,
            child: AbsorbPointer(
              child: TextFormField(
                controller: widget.controller,
                minLines: widget.minLines,
                keyboardType: TextInputType.multiline,
                maxLines: null,
                style: const TextStyle(
                  color: Colors.black,
                ),
                decoration: InputDecoration(
                  labelStyle: const TextStyle(color: Colors.black, fontSize: 12),
                  hintText: widget.hintText,
                  hintStyle: const TextStyle(color: Colors.grey, fontSize: 12),
                  border: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey),
                  ),
                ),
              ),
            ),
          ),
         
        ],
      ),
    );
  }
}

// Make sure to import ImagePicker

class AppTAImage extends StatefulWidget {
  const AppTAImage({
    Key? key,
    required this.controller,
    this.minLines = 6,
    this.hintText = '',
    this.labelText = '',
    required this.onTextChange,
    required this.onImageChange,
    this.imagePath,
  }) : super(key: key);

  final TextEditingController controller; // Controller for text input
  final String? imagePath; // Optional image path
  final String labelText;
  final String hintText;
  final int minLines;
  final Function(String text) onTextChange; // Callback for text changes
  final Function(String imagePath) onImageChange; // Callback for image changes

  @override
  _AppTAImageState createState() => _AppTAImageState();
}

class _AppTAImageState extends State<AppTAImage> {
  XFile? _selectedImage;

  @override
  void initState() {
    super.initState();
    if (widget.imagePath != null) {
      // Initialize with the provided image path
      _selectedImage = XFile(widget.imagePath!);
    }
  }

  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? pickedFile =
          await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _selectedImage = pickedFile;
        });
        // Call the onImageChange callback with the new image path
        widget.onImageChange(pickedFile.path);
      }
    } catch (e) {
      print('Error picking image: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: TextFormField(
            controller:
                widget.controller, // Use the passed controller for text input
            minLines: widget.minLines,
            keyboardType: TextInputType.name,
            maxLines: null,
            decoration: InputDecoration(
              hintText: widget.hintText,
              hintStyle: const TextStyle(color: Colors.grey, fontSize: 12),
              border: const UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.grey),
              ),
              enabledBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
              ),
              focusedBorder: const UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.grey),
              ),
              suffixIcon: IconButton(
                icon: const Icon(Icons.add_a_photo, color: Colors.grey),
                onPressed: _pickImage,
              ),
            ),
            onChanged: (val) {
              // Call the onTextChange callback with the current text
              widget.onTextChange(val);
            },
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          flex: 1,
          child: Container(
            width: 60,
            height: 50,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: _selectedImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      File(_selectedImage!.path),
                      fit: BoxFit.cover,
                    ),
                  )
                : const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.image, color: Colors.grey),
                        Text(
                          "No image\nuploaded",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}
