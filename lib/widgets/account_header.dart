import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:spot_on/widgets/app_txt.dart';

class AccountHeader extends StatelessWidget {
  const AccountHeader({
    Key? key,
    required this.imgUrl,
    required this.name,
    required this.role,
  }) : super(key: key);

  final String imgUrl;
  final String name;
  final String role;

  final double dimen = 100;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CachedNetworkImage(
          width: dimen,
          height: dimen,
          imageUrl: imgUrl,
          errorWidget: (context, url, error) => const Icon(Icons.error),
          imageBuilder: (context, imageProvider) => CircleAvatar(
            radius: dimen / 2,
            backgroundImage: imageProvider,
          ),
          placeholder: (context, url) => const CupertinoActivityIndicator(),
        ),
        const SizedBox(width: 20),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppTxt(
              text: name,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
            const SizedBox(height: 10),
            AppTxt(text: role),
          ],
        ),
      ],
    );
  }
}
