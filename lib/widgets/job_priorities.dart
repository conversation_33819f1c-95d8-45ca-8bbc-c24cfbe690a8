import 'package:spot_on/utils/imports.dart';

class JobPrioriity extends StatelessWidget {
  const JobPrioriity({
    Key? key,
    required this.text,
    required this.val,
    required this.groupVal,
    required this.onChanged,
    required this.color,
  }) : super(key: key);

  final String text;
  final Object val;
  final Color color;
  final Object groupVal;
  final Function onChanged;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Radio(
          value: val,
          groupValue: groupVal,
          visualDensity: const VisualDensity(
            horizontal: VisualDensity.minimumDensity,
            vertical: VisualDensity.minimumDensity,
          ),
          onChanged: (value) {
            onChanged(value);
          },
        ),
        Container(
          height: 32,
          padding: const EdgeInsets.fromLTRB(20, 10, 20, 10),
          decoration: BoxDecoration(
            color: color,
            borderRadius: const BorderRadius.all(
              Radius.circular(15.0),
            ),
          ),
          child: AppTxt(
            text: text,
            fontSize: 12,
            color: Colors.white,
          ),
        )
      ],
    );
  }
}
