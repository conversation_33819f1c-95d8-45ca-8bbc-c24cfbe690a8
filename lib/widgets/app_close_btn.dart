import 'package:flutter/material.dart';
import 'package:spot_on/utils/constants.dart';

class AppCloseBtn extends StatelessWidget {
  const AppCloseBtn({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(
        Icons.close_outlined,
        color: Colors.white,
      ),
      onPressed: () async {
        Navigator.pop(globalKey.currentContext!);
      },
    );
  }
}
