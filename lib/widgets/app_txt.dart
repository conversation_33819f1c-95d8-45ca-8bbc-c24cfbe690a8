import 'package:flutter/material.dart';
import 'package:spot_on/utils/constants.dart';

class AppTxt extends StatelessWidget {
  ///
  const AppTxt({
    Key? key,
    required this.text,
    this.fontSize = 14,
    this.alignment = TextAlign.start,
    this.color = Colors.black,
    this.fontFamily = fontBronova,
    this.fontWeight = FontWeight.normal,
    this.lines = 1,
    this.lineHeight = 1,
  }) : super(key: key);

  final int lines;
  final FontWeight fontWeight;
  final String text;
  final TextAlign alignment;
  final double fontSize;
  final Color color;
  final String fontFamily;
  final double lineHeight;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      child: Text(
        text,
        textAlign: alignment,
        maxLines: lines,
        style: TextStyle(
          color: color,
          fontSize: fontSize,
          fontWeight: fontWeight,
          height: lineHeight,
          fontFamily: fontFamily,
        ),
      ),
    );
  }
}
