import 'package:flutter/material.dart';

import '../utils/constants.dart';

class AppLogo extends StatelessWidget {
  ///
  const AppLogo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      // 'assets/images/spot_on_logo_splash.png',
      '',
      filterQuality: FilterQuality.high,
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) => Container(
        color: Colors.green,
        width: 100,
        height: 100,
      ),
    );
  }
}

class AppLogo1 extends StatelessWidget {
  ///
  const AppLogo1({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      logo,
      filterQuality: FilterQuality.high,
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) => Container(
        color: Colors.green,
        width: 100,
        height: 100,
      ),
    );
  }
}
