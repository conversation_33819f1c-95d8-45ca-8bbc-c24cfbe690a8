import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:spot_on/utils/constants.dart';

class AppTF extends StatelessWidget {
  ///
  const AppTF({
    Key? key,
    this.initialValue = '',
    this.hintText = '',
    this.isError = false,
    this.obscureText = false,
    this.textInputType = TextInputType.text,
    this.textFieldColor = Colors.grey,
    this.icon,
    this.iconColor,
    this.fontFamily = fontBronova,
    this.bottomMargin = 0.0,
    this.textStyle = const TextStyle(fontSize: 14.0),
    this.onSaved,
    this.hintStyle = const TextStyle(fontSize: 14.0),
    this.onTextChanged,
    this.endIcon,
    this.maxLines = 1,
    this.capitalizeHint = true,
    this.enabled = true,
  }) : super(key: key);

  final String hintText;
  final TextInputType textInputType;
  final Color textFieldColor;
  final Color? iconColor;
  final bool obscureText;
  final double bottomMargin;
  final TextStyle textStyle, hintStyle;
  final Function? onSaved;
  final Function? onTextChanged;
  final Widget? endIcon;
  final IconData? icon;
  final int maxLines;
  final bool isError;
  final bool capitalizeHint;
  final String initialValue;
  final String fontFamily;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(0),
      margin: EdgeInsets.only(bottom: bottomMargin),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 1, color: grey300!),
        ),
      ),
      child: TextFormField(
        initialValue: initialValue,
        style: textStyle,
        key: key,
        obscureText: obscureText,
        keyboardType: textInputType,
        maxLines: maxLines,
        enabled: enabled,
        onChanged: (val) async {
          if (null != onTextChanged) {
            onTextChanged!(val);
          }
        },
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.only(
            left: -16,
            top: 8,
            right: 8,
            bottom: 8,
          ),
          alignLabelWithHint: true,
          isDense: false,
          labelText: capitalizeHint ? hintText : hintText,
          fillColor: Colors.white,
          labelStyle: TextStyle(
            fontSize: 16,
            fontFamily: fontFamily,
            color: appBg,
            height: 0.2,
            fontWeight: FontWeight.bold,
          ),
          // hintText: captializeHint ? hintText : hintText,
          hintStyle: TextStyle(
            color: Colors.grey,
            fontSize: 18,
            fontFamily: fontFamily,
          ),
          icon: Visibility(
            visible: null != icon,
            child: Padding(
              padding: const EdgeInsets.only(
                top: 8,
                left: 8,
                right: 8,
              ),
              child: Icon(
                icon,
                color: appBg,
              ),
            ),
          ),
          suffixIcon: endIcon,
          border: InputBorder.none,
        ),
      ),
    );
  }
}

class AppTFController extends StatelessWidget {
  ///
  const AppTFController({
    Key? key,
    this.controller,
    this.initialValue = '',
    this.hintText = '',
    this.isError = false,
    this.obscureText = false,
    this.textInputType = TextInputType.text,
    this.textFieldColor = Colors.grey,
    this.icon,
    this.iconColor,
    this.fontFamily = fontBronova,
    this.bottomMargin = 0.0,
    this.textStyle = const TextStyle(fontSize: 14.0),
    this.onSaved,
    this.hintStyle = const TextStyle(fontSize: 14.0),
    this.onTextChanged,
    this.endIcon,
    this.maxLines = 1,
    this.capitalizeHint = true,
    this.enabled = true,
  }) : super(key: key);

  final TextEditingController? controller;
  final String hintText;
  final TextInputType textInputType;
  final Color textFieldColor;
  final Color? iconColor;
  final bool obscureText;
  final double bottomMargin;
  final TextStyle textStyle, hintStyle;
  final Function? onSaved;
  final Function? onTextChanged;
  final Widget? endIcon;
  final IconData? icon;
  final int maxLines;
  final bool isError;
  final bool capitalizeHint;
  final String initialValue;
  final String fontFamily;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(0),
      margin: EdgeInsets.only(bottom: bottomMargin),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 1, color: grey300!),
        ),
      ),
      child: TextFormField(
        controller: controller,
        initialValue: controller == null ? initialValue : null,
        style: textStyle,
        key: key,
        obscureText: obscureText,
        keyboardType: textInputType,
        maxLines: maxLines,
        enabled: enabled,
        onChanged: (val) async {
          if (null != onTextChanged) {
            onTextChanged!(val);
          }
        },
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.only(
            left: -16,
            top: 8,
            right: 8,
            bottom: 8,
          ),
          alignLabelWithHint: true,
          isDense: false,
          labelText: capitalizeHint ? hintText : hintText,
          fillColor: Colors.white,
          labelStyle: TextStyle(
            fontSize: 16,
            fontFamily: fontFamily,
            color: appBg,
            height: 0.2,
            fontWeight: FontWeight.bold,
          ),
          hintStyle: TextStyle(
            color: Colors.grey,
            fontSize: 18,
            fontFamily: fontFamily,
          ),
          icon: Visibility(
            visible: icon != null,
            child: Padding(
              padding: const EdgeInsets.only(
                top: 8,
                left: 8,
                right: 8,
              ),
              child: Icon(
                icon,
                color: appBg,
              ),
            ),
          ),
          suffixIcon: endIcon,
          border: InputBorder.none,
        ),
      ),
    );
  }
}

class AppTF1 extends StatelessWidget {
  ///
  const AppTF1({
    Key? key,
    this.initialValue = '',
    this.hintText = '',
    this.isError = false,
    this.obscureText = false,
    this.textInputType = TextInputType.number,
    this.textFieldColor = Colors.grey,
    this.icon,
    this.iconColor,
    this.fontFamily = fontBronova,
    this.bottomMargin = 0.0,
    this.textStyle = const TextStyle(fontSize: 14.0),
    this.onSaved,
    this.hintStyle = const TextStyle(fontSize: 14.0),
    this.onTextChanged,
    this.endIcon,
    this.maxLines = 1,
    this.capitalizeHint = true,
    this.enabled = true,
  }) : super(key: key);

  final String hintText;
  final TextInputType textInputType;
  final Color textFieldColor;
  final Color? iconColor;
  final bool obscureText;
  final double bottomMargin;
  final TextStyle textStyle, hintStyle;
  final Function? onSaved;
  final Function? onTextChanged;
  final Widget? endIcon;
  final IconData? icon;
  final int maxLines;
  final bool isError;
  final bool capitalizeHint;
  final String initialValue;
  final String fontFamily;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(0),
      margin: EdgeInsets.only(bottom: bottomMargin),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 1, color: grey300!),
        ),
      ),
      child: TextFormField(
        initialValue: initialValue,
        style: textStyle,
        key: key,
        obscureText: obscureText,
        keyboardType: TextInputType.number, // Ensures numeric keyboard is shown
        maxLines: maxLines,
        enabled: enabled,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly, // Allows only digits
          LengthLimitingTextInputFormatter(10), // Limits the input to 10 digits
        ],
        onChanged: (val) async {
          if (null != onTextChanged) {
            onTextChanged!(val);
          }
        },
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.only(
            left: -16,
            top: 8,
            right: 8,
            bottom: 8,
          ),
          alignLabelWithHint: true,
          isDense: false,
          labelText: capitalizeHint ? hintText : hintText,
          fillColor: Colors.white,
          labelStyle: TextStyle(
            fontSize: 16,
            fontFamily: fontFamily,
            color: appBg,
            height: 0.2,
            fontWeight: FontWeight.bold,
          ),
          hintStyle: TextStyle(
            color: Colors.grey,
            fontSize: 18,
            fontFamily: fontFamily,
          ),
          icon: Visibility(
            visible: null != icon,
            child: Padding(
              padding: const EdgeInsets.only(
                top: 8,
                left: 8,
                right: 8,
              ),
              child: Icon(
                icon,
                color: appBg,
              ),
            ),
          ),
          suffixIcon: endIcon,
          border: InputBorder.none,
        ),
      ),
    );
  }
}

class AppTF2 extends StatelessWidget {
  ///
  const AppTF2({
    Key? key,
    this.controller, // Add the controller parameter
    this.initialValue = '',
    this.hintText = '',
    this.isError = false,
    this.obscureText = false,
    this.textInputType = TextInputType.text,
    this.textFieldColor = Colors.grey,
    this.icon,
    this.iconColor,
    this.fontFamily = fontBronova,
    this.bottomMargin = 0.0,
    this.textStyle = const TextStyle(fontSize: 14.0),
    this.onSaved,
    this.hintStyle = const TextStyle(fontSize: 14.0),
    this.onTextChanged,
    this.endIcon,
    this.maxLines = 1,
    this.capitalizeHint = true,
    this.enabled = true,
  }) : super(key: key);

  final TextEditingController? controller; // Add this line
  final String hintText;
  final TextInputType textInputType;
  final Color textFieldColor;
  final Color? iconColor;
  final bool obscureText;
  final double bottomMargin;
  final TextStyle textStyle, hintStyle;
  final Function? onSaved;
  final Function? onTextChanged;
  final Widget? endIcon;
  final IconData? icon;
  final int maxLines;
  final bool isError;
  final bool capitalizeHint;
  final String initialValue;
  final String fontFamily;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(0),
      margin: EdgeInsets.only(bottom: bottomMargin),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 1, color: grey300!),
        ),
      ),
      child: TextFormField(
        controller: controller, // Use the controller
        initialValue:
            controller == null ? initialValue : null, // Ensure no conflict
        style: textStyle,
        key: key,
        obscureText: obscureText,
        keyboardType: textInputType,
        maxLines: maxLines,
        enabled: enabled,
        onChanged: (val) async {
          if (null != onTextChanged) {
            onTextChanged!(val);
          }
        },
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.only(
            left: -16,
            top: 8,
            right: 8,
            bottom: 8,
          ),
          alignLabelWithHint: true,
          isDense: false,
          labelText: capitalizeHint ? hintText : hintText,
          fillColor: Colors.white,
          labelStyle: TextStyle(
            fontSize: 16,
            fontFamily: fontFamily,
            color: appBg,
            height: 0.2,
            fontWeight: FontWeight.bold,
          ),
          hintStyle: TextStyle(
            color: Colors.grey,
            fontSize: 18,
            fontFamily: fontFamily,
          ),
          icon: Visibility(
            visible: icon != null,
            child: Padding(
              padding: const EdgeInsets.only(
                top: 8,
                left: 8,
                right: 8,
              ),
              child: Icon(
                icon,
                color: appBg,
              ),
            ),
          ),
          suffixIcon: endIcon,
          border: InputBorder.none,
        ),
      ),
    );
  }
}
