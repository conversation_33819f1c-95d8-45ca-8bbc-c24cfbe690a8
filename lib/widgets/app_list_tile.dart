import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:spot_on/widgets/app_tf.dart';

import 'app_txt.dart';

class AppListTile extends StatelessWidget {
  const AppListTile({
    Key? key,
    required this.title,
    required this.value,
    this.icon,
    this.editMode = false,
    this.onTextUpdate,
  }) : super(key: key);

  final String title;
  final String value;
  final Widget? icon;
  final bool editMode;
  final Function? onTextUpdate;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: !editMode,
          child: AppTxt(
            text: title,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 15),
        Visibility(
          visible: !editMode,
          child: AppTxt(
            text: value,
            fontSize: 20,
            fontWeight: FontWeight.normal,
          ),
        ),
        Visibility(
          visible: editMode,
          child: AppTF(
            hintText: title,
            maxLines: 1,
            initialValue: value,
            onTextChanged: (val) async {
              if (null != onTextUpdate) {
                onTextUpdate!(val);
              }
            },
          ),
        )
      ],
    );
  }
}

class AppListTile1 extends StatelessWidget {
  const AppListTile1({
    Key? key,
    required this.title,
    required this.value,
    this.icon,
    this.editMode = false,
    this.onTextUpdate,
  }) : super(key: key);

  final String title;
  final String value;
  final Widget? icon;
  final bool editMode;
  final Function? onTextUpdate;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: !editMode,
          child: AppTxt(
            text: title,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 15),
        Visibility(
          visible: !editMode,
          child: AppTxt(
            text: value,
            fontSize: 20,
            fontWeight: FontWeight.normal,
          ),
        ),
        Visibility(
          visible: editMode,
          child: AppTF1(
            hintText: title,
            maxLines: 1,
            initialValue: value,
            onTextChanged: (val) async {
              if (null != onTextUpdate) {
                onTextUpdate!(val);
              }
            },
          ),
        ),
      ],
    );
  }
}
