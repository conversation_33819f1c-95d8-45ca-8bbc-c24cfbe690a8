import 'package:flutter/material.dart';
import 'package:spot_on/utils/utils.dart';

class GlobalWidget extends StatelessWidget {
  //
  final Widget child;

  const GlobalWidget({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        Utils.hideKeyboard(context);
      },
      child: child,
    );
  }
}
