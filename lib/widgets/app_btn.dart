import 'package:flutter/material.dart';
import 'package:spot_on/utils/constants.dart';

import 'app_txt.dart';

class AppBtn extends StatelessWidget {
  final String text;
  final Function onPress;
  final Color? color;
  final Color loadingColor;
  final FontWeight fontWeight;
  final Color borderColor;
  final Color bgColor;
  final bool loading;
  final double height;

  const AppBtn({
    Key? key,
    required this.text,
    this.color,
    this.fontWeight = FontWeight.normal,
    this.height = 40,
    required this.onPress,
    this.loading = false,
    this.borderColor = appColor,
    this.bgColor = Colors.transparent,
    this.loadingColor = Colors.white,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (loading) {
      return Container(
        height: height,
        alignment: Alignment.center,
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: bgColor,
          border: Border.all(
            color: borderColor,
          ),
          borderRadius: BorderRadius.circular(height / 3),
        ),
        child: SizedB<PERSON>(
          width: height - 20,
          height: height,
          child: const CircularProgressIndicator(
            color: appBg,
            strokeWidth: 2,
          ),
        ),
      );
    }
    return InkWell(
      onTap: () async {
        onPress();
      },
      child: Material(
        elevation: 0.5,
        borderRadius: BorderRadius.circular(height / 3),
        child: Container(
          height: height,
          alignment: Alignment.center,
          padding: const EdgeInsets.only(
            left: 20,
            right: 20,
            top: 4,
          ),
          decoration: BoxDecoration(
            color: bgColor,
            // border: Border.all(
            //   color: borderColor,
            // ),
            borderRadius: BorderRadius.circular(height / 3),
          ),
          child: AppTxt(
            text: text,
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
      ),
    );
  }
}
