import 'package:location/location.dart';
import 'package:spot_on/utils/imports.dart';

class AppLocation {
  Future<bool> isLocationEnabled() async {
    Location location = Location();
    bool serviceEnabled;
    serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await location.requestService();
      if (!serviceEnabled) {
        showSnackBar(
          "Your device's location is turned off. Please turn it on to continue.",
          success: false,
        );
        return false;
      }
    }
    return true;
  }

  Future<bool> isLocationGranted() async {
    Location location = Location();
    PermissionStatus permissionGranted;
    permissionGranted = await location.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await location.requestPermission();
      if (permissionGranted != PermissionStatus.granted) {
        if (permissionGranted == PermissionStatus.deniedForever) {
          showSnackBar(
            "Please grant location permissions for 'Spot On' app in your device's settings to continue with confirming spot pickup.",
            success: false,
          );
        } else {
          showSnackBar(
            "Please grant location permissions for 'Spot On' app to continue with confirming spot pickup.",
            success: false,
          );
        }
        return false;
      }
    }
    return true;
  }

  requestLocationService() async {
    Location location = Location();
    bool serviceEnabled;
    PermissionStatus permissionGranted;
    serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await location.requestService();
      if (!serviceEnabled) {
        // showSnackBar(
        //     "Your device's location is currently turned off. To ensure the app functions properly, you can turn it on later when required.");
        return;
        // return Future.error("Location disabled!");
      }
    }
    permissionGranted = await location.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await location.requestPermission();
      if (permissionGranted != PermissionStatus.granted) {
        // showSnackBar(
        //     "You haven't granted the necessary location permissions for the app. To ensure the app functions properly, you can grant them later when required.");
        return;
        // return Future.error("Location permission denied!");
      }
    }
  }

  Future<LocationData?> getLocation() async {
    Location location = Location();

    bool serviceEnabled;
    PermissionStatus permissionGranted;
    LocationData locationData;

    serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await location.requestService();
      if (!serviceEnabled) {
        return Future.error("Location disabled!");
      }
    }

    permissionGranted = await location.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await location.requestPermission();
      if (permissionGranted != PermissionStatus.granted) {
        return Future.error("Location permission denied!");
      }
    }

    locationData = await location.getLocation();
    return locationData;
  }
}
