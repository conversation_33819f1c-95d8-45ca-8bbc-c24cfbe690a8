import 'package:spot_on/models/cliient_list_model.dart';
import 'package:spot_on/models/login/login_response_model.dart';
import 'package:spot_on/providers/create_job_state.dart';
import 'package:spot_on/providers/jobs_state.dart';
import 'package:spot_on/utils/imports.dart';
import 'package:spot_on/utils/push_notifications.dart';

class LoginState extends ChangeNotifier {
  //
  bool loginLoading = false;
  bool clientsLoading = false;
  bool loggingOut = false;
  bool showPwd = false;
  bool resetPwdLoading = false;

  String userName = '';
  String password = '';
  String email = '';
  String errorMessage = '';
  ClientListModel clientListModel = ClientListModel(list: []);
  SpotOnClient? selectedClient;

  //
  String p1 = '';
  String p2 = '';
  String emailToken = '';

  refresh() async {
    notifyListeners();
  }

  setShowPwdLoading(bool show) async {
    resetPwdLoading = show;
    notifyListeners();
  }

  setShowPwd(bool show) async {
    showPwd = show;
    notifyListeners();
  }

  setLoggingOut(bool loading) async {
    loggingOut = loading;
    notifyListeners();
  }

  setClientsLoading(bool loading) async {
    clientsLoading = loading;
    notifyListeners();
  }

  setLoading(bool loading) async {
    loginLoading = loading;
    notifyListeners();
  }

  setErrorMessage(String error) async {
    errorMessage = error;
    notifyListeners();
  }

  areValidFields() {
    if (userName.isEmpty) {
      showSnackBar('Please enter your Email.', success: false);
      return false;
    }
    if (!Utils.isValidEmail(userName)) {
      showSnackBar('Please enter a valid Email.', success: false);
      return false;
    }
    if (password.isEmpty) {
      showSnackBar('Please enter your password.', success: false);
      return false;
    }
    return true;
  }

  doLogin() async {
    if (!areValidFields()) {
      return;
    }

    setLoading(true);
    setErrorMessage('');
    var result = await Services.loginUser(userName, password);
    if (result is Success) {
      Preferences.loginResponseModel = result.response as LoginResponseModel;
      // String role = Preferences.loginResponseModel!.roles.first;
      // if (role.toLowerCase().contains('client')) {
      //   Preferences.loginResponseModel = null;
      //   setLoading(false);
      //   showSnackBar('Client Not Authorized to use the Spot On App');
      //   return;
      // }
      Preferences.saveLoginInfo(Preferences.loginResponseModel!);
      await getClients();
      JobsState jobsState = globalKey.currentContext!.read<JobsState>();
      getJobsForHomeTab();
      jobsState.getJobs();
      CreateJobState createJobState = globalKey.currentContext!.read<CreateJobState>();
      createJobState.getSpotOnLocations();
      createJobState.getTruckList();
      createJobState.getEntryExitReportList();
      createJobState.selectedCarrier = createJobState.carrierList.first;
      HomeState homeState = globalKey.currentContext!.read<HomeState>();
      homeState.setDashboardTabs(Preferences.loginResponseModel!.roles.first);
      if (await homeState.askPushPermission()) {
        await homeState.registerDevice();
        FBPushNotification.initListeners();
      }
    }
    if (result is Failure) {
      errorMessage = result.response as String;
      setErrorMessage(errorMessage);
    }
    setLoading(false);
  }

  doLogout() async {
    setLoggingOut(true);
    var result = await Services.doLogout();
    if (result is Success) {
      userName = '';
      password = '';
      email = '';
    }
    if (result is Failure) {
      //
    }
    setLoggingOut(false);
  }

  getClients({bool fromHome = false}) async {
    setClientsLoading(true);
    var result = await Services.getClients();
    if (result is Success) {
      clientListModel = result.response as ClientListModel;
      SpotOnClient? selectedClient = await Preferences.getSelectedClient();
      if (null == selectedClient) {
        selectedClient = clientListModel.list.first;
      } else {
        for (var client in clientListModel.list) {
          client.selected = client.clientId == selectedClient.clientId;
        }
      }
      await Preferences.saveSelectedClient(selectedClient);
      if (!fromHome) {
        showLoginSuccessScreen();
        JobsState jobsState = globalKey.currentContext!.read<JobsState>();
        getJobsForHomeTab();
        jobsState.getJobs();
      }
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
      }
    }
    setClientsLoading(false);
  }

  doResetPassword() async {
    setShowPwdLoading(true);
    var result = await Services.resetPassword(email);
    if (result is Success) {
      showSnackBar('Please check your inbox and enter your token');
      closeScreen();
      openSsetNewPwdScreen();
    }
    if (result is Failure) {
      String errorMessage = result.response as String;
      showSnackBar(errorMessage);
    }
    setShowPwdLoading(false);
  }

  doResetPassword2() async {
    setShowPwdLoading(true);
    var result = await Services.resetPassword2(p1, p2, emailToken);
    if (result is Success) {
      showSnackBar('Your password has been reset');
      closeScreen();
    }
    if (result is Failure) {
      String errorMessage = result.response as String;
      showSnackBar(errorMessage);
    }
    setShowPwdLoading(false);
  }
}
