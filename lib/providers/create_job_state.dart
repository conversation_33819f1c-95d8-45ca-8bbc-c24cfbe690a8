import 'package:autocomplete_textfield/autocomplete_textfield.dart';
import 'package:spot_on/models/cliient_list_model.dart';
import 'package:spot_on/models/confirm_drop_spot_id_model.dart';
import 'package:spot_on/models/drivers_list.dart';
import 'package:spot_on/models/entry_exit_report_list_model.dart';
import 'package:spot_on/models/job/job_request_model.dart';
import 'package:spot_on/models/job/jobs_list_model.dart';
import 'package:spot_on/models/spot_on_locations.dart';
import 'package:spot_on/models/spots_list.dart';
import 'package:spot_on/models/truck_list_model.dart';
import 'package:spot_on/providers/jobs_state.dart';
import 'package:spot_on/providers/new_trailer_state.dart';
import 'package:spot_on/utils/app_logger.dart';
import 'package:spot_on/utils/imports.dart';

class CreateJobState extends ChangeNotifier {
  bool spotsLoading = false;
  bool spotOnLocationsLoading = false;
  bool truckListLoading = false;
  bool driverListLoading = false;
  bool createJobLoading = false;
  bool entryExitLoading = false;
  bool entryExitReportLoading = false;
  bool jobDropSpotUpdateLoading = false;
  bool jobDropSpotUpdateError = false;
  bool newLoading = false;

  SpotsList pickupSpotsList = SpotsList(list: []);
  SpotsList dropSpotsList = SpotsList(list: []);
  TruckListModel truckListModel = TruckListModel(list: []);
  Spot? selectedPickupSpot;
  Spot? selectedDropSpot;
  TruckDetail? selectedTruckDetail;
  FleetStatus? selectedFleetStatus;
  Driver? selectedDriver;
  SpotOnLocation? selectedPickUpLocation;
  SpotOnLocation? selectedDropLocation;
  Job? selectedJob;
  SpotOnLocationList spotOnLocationList = SpotOnLocationList(list: []);
  String? selectedCarrier;
  DriversList driversList = DriversList(list: []);
  List<FleetStatus> fleetStatuses = [
    const FleetStatus('EMPTY', 'Empty'),
    const FleetStatus('FULL', 'Full')
  ];

  JobRequestModel jobRequestModel = JobRequestModel(
    assignedToUserId: '',
    //---commented--bucket
    bucket: '',
    description: '',
    dropLocationId: '',
    dropSpotId: '',
    fleetId: '',
    fleetStatus: null,
    pickupLocationId: '',
    pickupSpotId: '',
    priority: 'HIGH',
  );

  ConfirmDropUpdateDropModel confirmDropUpdateDropModel =
      ConfirmDropUpdateDropModel(
    sequenceAsn: '',
    assignedToUserId: '',
    description: '',
    dropLocationId: '',
    dropSpotId: '',
    fleetId: '',
    fleetStatus: null,
    pickupLocationId: '',
    pickupSpotId: '',
    priority: '',
  );

  GlobalKey<AutoCompleteTextFieldState<TruckDetail>> autoCompleteKey =
      GlobalKey();
  TextEditingController autoCompleteController = TextEditingController();

  // entry exit
  bool exitSelected = false;
  SpotOnLocation? selectedEntryExitLocation;
  List<String> carrierList = ['Fedex', 'UPS'];
  String entryExitNotes = '';
  EntryExitReportListModel entryExitReportListModel =
      EntryExitReportListModel(list: [], page: 0, size: 0, totalElements: 0);

  refresh() async {
    notifyListeners();
  }

  setJobDropSpotUpdateLoading(bool loading) async {
    jobDropSpotUpdateLoading = loading;
    notifyListeners();
  }

  setEntryExitReportLoading(bool loading) async {
    entryExitReportLoading = loading;
    notifyListeners();
  }

  setExitLoading(bool loading) async {
    entryExitLoading = loading;
    notifyListeners();
  }

  Future<void> showNewLoading() async {
    newLoading = true;
    notifyListeners();
    await Future.delayed(const Duration(seconds: 2));
    newLoading = false;
    notifyListeners();
  }

  setExitSelected(bool selected) async {
    exitSelected = selected;
    notifyListeners();
  }

  setTruckListLoading(bool loading) async {
    truckListLoading = loading;
    notifyListeners();
  }

  setDriverListLoading(bool loading) async {
    driverListLoading = loading;
    notifyListeners();
  }

  setSpotsLoading(bool loading) async {
    spotsLoading = loading;
    notifyListeners();
  }

  setCreateJobLoading(bool loading) async {
    createJobLoading = loading;
    notifyListeners();
  }

  setSpotOnLocationsLoading(bool loading) async {
    spotOnLocationsLoading = loading;
    notifyListeners();
  }

  clearAllLists() async {
    pickupSpotsList.list.clear();
    dropSpotsList.list.clear();
    truckListModel.list.clear();
    spotOnLocationList.list.clear();
    driversList.list.clear();
  }

  clearSelections() async {
    selectedPickUpLocation = null;
    selectedDropLocation = null;
    selectedPickupSpot = null;
    selectedDropSpot = null;
    selectedTruckDetail = null;
    setDefaultJob();
  }

  setDefaultJob() async {
    jobRequestModel = JobRequestModel(
      //commented---bucket
      bucket: '',
      assignedToUserId: '',
      description: '',
      dropLocationId: '',
      dropSpotId: '',
      fleetId: '',
      fleetStatus: null,
      pickupLocationId: '',
      pickupSpotId: '',
      priority: 'HIGH',
    );
  }

  clearSelection() {
    clearNotes();
    selectedJob = null;
    selectedCarrier = null;
    selectedDriver = null;
    selectedDropLocation = null;
    selectedDropSpot = null;
    selectedEntryExitLocation = null;
    selectedPickUpLocation = null;
    selectedPickupSpot = null;
    selectedTruckDetail = null;
    jobRequestModel.dropSpotId = null;
    jobRequestModel.pickupSpotId = null;
    selectedFleetStatus = null;
    entryExitNotes = '';
    spotsLoading = false;
    createJobLoading = false;
    driverListLoading = false;
    entryExitLoading = false;
    entryExitReportLoading = false;
    jobDropSpotUpdateLoading = false;
    jobDropSpotUpdateLoading = false;
    spotOnLocationsLoading = false;
    spotsLoading = false;
    truckListLoading = false;
    autoCompleteController.text = '';
  }

  getSpots({required String locationId, bool drop = false}) async {
    setSpotsLoading(true);
    SpotOnClient? client = await Preferences.getSelectedClient();
    var result = await Services.getSpots(client!.clientId!, locationId);
    if (result is Success) {
      if (drop) {
        dropSpotsList = result.response as SpotsList;
        if (null != selectedJob) {
          if (null != selectedJob!.dropSpot) {
            selectedDropSpot = dropSpotsList.list.firstWhere(
                (element) => element.spotId == selectedJob!.dropSpot!.spotId,
                orElse: () => Spot());
          }
          if (null != selectedJob!.fleetStatus &&
              selectedJob!.fleetStatus!.trim().isNotEmpty) {
            print(selectedJob!.fleetStatus);
            print(selectedJob!.fleetStatus!.trim().isNotEmpty);
            selectedFleetStatus = fleetStatuses.firstWhere((element) {
              return (element.id == selectedJob!.fleetStatus);
            });
          }
        }
      } else {
        pickupSpotsList = result.response as SpotsList;
        if (null != selectedJob && null != selectedJob!.pickupSpot) {

          print('element---${selectedJob!.pickupSpot!.spotId    }');
          selectedPickupSpot = pickupSpotsList.list.firstWhere(
              (element) => element.spotId == selectedJob!.pickupSpot!.spotId);
        }
      }
    }
    if (result is Failure) {
      printLog('Get Spots Failed');
    }
    setSpotsLoading(false);
  }

  getPickUpSpots({required String locationId}) async {
    setSpotsLoading(true);
    SpotOnClient? client = await Preferences.getSelectedClient();
    var result = await Services.getSpots(client!.clientId!, locationId);
    if (result is Success) {
      pickupSpotsList = result.response as SpotsList;
      if (null != selectedJob && null != selectedJob!.pickupSpot) {
        selectedPickupSpot = pickupSpotsList.list.firstWhere(
            (element) => element.spotId == selectedJob!.pickupSpot!.spotId);
      }
    }
    if (result is Failure) {
      printLog('Get Spots Failed');
    }
    setSpotsLoading(false);
  }

  getDropOffSpots({required String locationId}) async {
    setSpotsLoading(true);
    SpotOnClient? client = await Preferences.getSelectedClient();
    var result = await Services.getSpots(client!.clientId!, locationId);
    if (result is Success) {
      dropSpotsList = result.response as SpotsList;
      if (null != selectedJob) {
        if (null != selectedJob!.dropSpot) {
          selectedDropSpot = dropSpotsList.list.firstWhere(
              (element) => element.spotId == selectedJob!.dropSpot!.spotId);
        }
        // if (null != selectedJob!.fleetStatus &&
        //     selectedJob!.fleetStatus!.trim().isNotEmpty) {
        //   selectedFleetStatus = fleetStatuses
        //       .firstWhere((element) => element.id == selectedJob!.fleetStatus);
        // }
      }
    }
    if (result is Failure) {
      printLog('Get Spots Failed');
    }
    setSpotsLoading(false);
  }

  getSpotOnLocations() async {
    setSpotOnLocationsLoading(true);
    SpotOnClient? client = await Preferences.getSelectedClient();
    var result = await Services.getLocationSpots(client!.clientId!,'');//edit
    if (result is Success) {
      spotOnLocationList = result.response as SpotOnLocationList;
      if (spotOnLocationList.list.isNotEmpty) {
        // selectedPickUpLocation = spotOnLocationList.list.first;
        // selectedEntryExitLocation = spotOnLocationList.list.first;
        // selectedDropLocation = spotOnLocationList.list.first;
        // jobRequestModel.pickupLocationId = selectedPickUpLocation!.locationId!;
        // jobRequestModel.dropLocationId = selectedDropLocation!.locationId!;
        // getSpots(locationId: jobRequestModel.pickupLocationId);
      }
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
    }
    setSpotOnLocationsLoading(false);
  }

  getDropOffLocations(String clientId,String id) async {
    setSpotOnLocationsLoading(true);
    var result = await Services.getLocationSpots(clientId,id);//edit
    if (result is Success) {
      spotOnLocationList = result.response as SpotOnLocationList;
      if (spotOnLocationList.list.isNotEmpty) {
        print('drop-----data');
        if (spotOnLocationList.list.any((element) =>
    element.locationId?.toString() ==
    selectedJob?.dropLocation?.locationId?.toString())) {
  selectedDropLocation = spotOnLocationList.list.firstWhere(
    (element) =>
        element.locationId?.toString() ==
        selectedJob?.dropLocation?.locationId?.toString(),
  );
} else {
  // If there's no matching element, leave selectedDropLocation as null
  // (It's already declared as SpotOnLocation? and initialized to null,
  // so no need to explicitly set it to null here.)
}
//        selectedDropLocation = spotOnLocationList.list.firstWhere(
//   (element) =>
//       element.locationId?.toString() == // Added null check here
//       selectedJob?.dropLocation?.locationId?.toString(), // And here
//   orElse: () => SpotOnLocation(),
// );
      }
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
    }
    setSpotOnLocationsLoading(false);
  }

  getTruckList() async {
    setTruckListLoading(true);
    var result = await Services.getTruckList();
    if (result is Success) {
      truckListModel = result.response as TruckListModel;
      if (truckListModel.list.isNotEmpty) {
        NewTrailerState newTrailerState =
            globalKey.currentContext!.read<NewTrailerState>();
        if (newTrailerState.newTruckAdded) {
          selectedTruckDetail = truckListModel.list.firstWhere((element) =>
              element.unitNumber ==
              newTrailerState.newTrailerReqModel.unitNumber);
        }
        if (null != selectedTruckDetail) {
          jobRequestModel.fleetId = selectedTruckDetail!.fleetId!;
        }
      }
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
    }
    setTruckListLoading(false);
  }

  getTruckListNew(String unitNumber) async {
    setTruckListLoading(true);
    var result = await Services.getTruckListNew(unitNumber);
    if (result is Success) {
      truckListModel = result.response as TruckListModel;
      if (truckListModel.list.isNotEmpty) {
        NewTrailerState newTrailerState =
            globalKey.currentContext!.read<NewTrailerState>();
        if (newTrailerState.newTruckAdded) {
          selectedTruckDetail = truckListModel.list.firstWhere((element) =>
              element.unitNumber ==
              newTrailerState.newTrailerReqModel.unitNumber);
        }
        if (null != selectedTruckDetail) {
          jobRequestModel.fleetId = selectedTruckDetail!.fleetId!;
        }
      }
      notifyListeners();
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
    }
    setTruckListLoading(false);
  }

  getEntryExitReportList() async {
    setEntryExitReportLoading(true);
    var result = await Services.getEntryExitReportList();
    if (result is Success) {
      entryExitReportListModel = result.response as EntryExitReportListModel;
      notifyListeners();
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
    }
    setEntryExitReportLoading(false);
  }

  getDriversList(String loc1,String loc2) async {
    setDriverListLoading(true);
    var result = await Services.getDriversList(loc1,loc2);
    if (result is Success) {
      driversList = result.response as DriversList;
      String role = Preferences.loginResponseModel!.roles.first.toLowerCase();
      if (role.contains('spotter') || role.contains('guard')) {
        driversList.list.removeWhere((element) {
          return !(element.roles.first.roleName!
                  .toLowerCase()
                  .contains('driver') ||
              element.roles.first.roleName!.toLowerCase().contains('spotter'));
        });
      }
      if (driversList.list.isNotEmpty) {
        if (isDriver()) {
          for (Driver driver in driversList.list) {
            if (driver.userId == Preferences.loginResponseModel?.userId) {
              selectedDriver = driver;
              break;
            }
          }
        } else {
          //selectedDriver = driversList.list.first;
        }
        if (isSpotter()) {
          print('ri-----');
          for (Driver driver in driversList.list) {
            if (driver.userId == Preferences.loginResponseModel?.userId) {
              selectedDriver = driver;
              break;
            }
          }
        }
        if (null != selectedDriver) {
          jobRequestModel.assignedToUserId = selectedDriver!.userId!;
        }
      }
      notifyListeners();
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
      }
    }
    setDriverListLoading(false);
  }

  createJob() async {
    if (createJobLoading) {
      return;
    }
    setCreateJobLoading(true);
    print(
        'object--create---------1---${jobRequestModel.assignedToUserId}----${jobRequestModel.description}---1--${jobRequestModel.dropLocationId}---1--${jobRequestModel.fleetId}--1---${jobRequestModel.pickupLocationId}---1--${jobRequestModel.pickupSpotId}--1---${jobRequestModel.assignedToUserId}---1--${jobRequestModel.dropSpotId}---1--${jobRequestModel.priority}----1--${jobRequestModel.fleetStatus}-----');

    var result = await Services.createJob(jobRequestModel);
    print('res------${result.toString()}');
    if (result is Success) {
      print('risaj----success');

      closeScreen();
      JobsState jobsState = globalKey.currentContext!.read<JobsState>();
      HomeState homeState = globalKey.currentContext!.read<HomeState>();
      CreateJobState createJobState =
          globalKey.currentContext!.read<CreateJobState>();

      getJobsForHomeTab();
      jobsState.getJobs();
      homeState.currentTabIndex = 0;
      createJobState.jobRequestModel.priority = 'HIGH';
      notifyListeners();
      showToast('Job Created Successfully');
      clearNotes();
    }
    if (result is Failure) {
      print('risaj----s');

      if (result.code == unauthorized) {
        print('risaj----s00');

        openLogin();
        return;
      }
      showSnackBar(
        result.response.toString(),
        success: false,
        delay: 10,
      );
      clearNotes();
    }
    setCreateJobLoading(false);
  }

  void clearNotes() {
    jobRequestModel.description = '';
  }

  doFleetEntryExit() async {
    setExitLoading(true);
    var result = await Services.doFleetEntryExit(
      selectedTruckDetail!.fleetId!,
      entryExitNotes,
      exitSelected ? 'EXIT' : 'ENTRY',
      selectedEntryExitLocation!.locationId!,
    );
    if (result is Success) {
      showFleetEntrySuccessBottomSheet(
        globalKey.currentContext!,
        isExit: exitSelected,
      );
      getEntryExitReportList();
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
      showSnackBar(
        result.response.toString(),
        delay: 10,
        success: false,
      );
    }
    setExitLoading(false);
  }

  doJobUpdateWithDropSpotId(Job job, Spot spot,
      {bool isPickUpUpdate = false}) async {
    if (jobDropSpotUpdateLoading) {
      return;
    }
    setJobDropSpotUpdateLoading(true);
    jobDropSpotUpdateError = false;

    var result = await Services.doJobUpdateWithDropSpotId(
      confirmDropUpdateDropModel: confirmDropUpdateDropModel,
      jobId: job.jobId!,
    );
    if (result is Success) {
      if (!isPickUpUpdate) {
        job.dropSpot = PSpot(
          spotId: spot.spotId,
          spotName: spot.spotName,
          latitude: spot.latitude,
          longitude: spot.longitude,
          locationId: spot.locationId,
          isActive: spot.isActive ?? false,
          remarks: spot.remarks,
          status: spot.status,
          type: spot.type,
        );
        selectedDropSpot = spot;
        showToast('Drop Spot Updated');
      } else {
        job.pickupSpot = PSpot(
          spotId: spot.spotId,
          spotName: spot.spotName,
          latitude: spot.latitude,
          longitude: spot.longitude,
          locationId: spot.locationId,
          isActive: spot.isActive ?? false,
          remarks: spot.remarks,
          status: spot.status,
          type: spot.type,
        );
        selectedPickupSpot = spot;
        printLog(spot.spotName);

        showToast('Pickup Spot Updated');
        printLog('Job Updated');
      }
      printLog('Job Updated');
    }
    if (result is Failure) {
      // print("${result.response} 1");
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
      jobDropSpotUpdateError = true;

      showToast(result.response.toString());
      // showSnackBar(
      //   result.response.toString(),
      //   delay: 10,
      // );
    }

    setJobDropSpotUpdateLoading(false);
  }
}

class FleetStatus {
  const FleetStatus(this.id, this.name);

  final String id;
  final String name;
}
