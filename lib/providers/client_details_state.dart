import 'package:spot_on/models/client_details_new_model.dart';
import 'package:spot_on/models/cliient_list_model.dart';
import 'package:spot_on/models/job/jobs_list_model.dart';
import 'package:spot_on/utils/imports.dart';

class ClientDetailsState {
  static ClientDetailsNewModel? clientDetailsNewModelres = ClientDetailsNewModel();

    getClientDetails() async {
    // setHomeJobsLoading(true);
        SpotOnClient? client = await Preferences.getSelectedClient();

    var result = await Services.getClientDetails(client!.clientId.toString());
    if (result is Success) {
      ClientDetailsNewModel clientDetailsNewModel = result.response as ClientDetailsNewModel;
      clientDetailsNewModelres = clientDetailsNewModel;
    }

    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
      showSnackBar(
        result.response.toString(),
        delay: 10,
      );
    }
    // setHomeJobsLoading(false);
  }
}
