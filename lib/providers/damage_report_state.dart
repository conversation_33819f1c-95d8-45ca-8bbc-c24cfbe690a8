import 'package:autocomplete_textfield/autocomplete_textfield.dart';
import 'package:spot_on/utils/imports.dart';

import '../models/damage/damage_report.dart';
import '../models/truck_list_model.dart';
import '../utils/app_logger.dart';

class DamageReportState extends ChangeNotifier {
  bool isLoading = false;
  String? report;
  String? comment;
  String? fleetId;
  // List<FleetStatus> fleetStatuses = [const FleetStatus('EMPTY', 'Empty'), const FleetStatus('FULL', 'Full')];
  FleetStatus? selectedFleetStatus;
  // bool truckListLoading = false;
  // TruckListModel truckListModel = TruckListModel(list: []);

  GlobalKey<AutoCompleteTextFieldState<TruckDetail>> autoCompleteKey = GlobalKey();
  TextEditingController autoCompleteController = TextEditingController();
  TruckDetail? selectedTruckDetail;
  refresh() async {
    notifyListeners();
  }

  showLoading() {
    isLoading = true;
    notifyListeners();
  }

  clearAll() async {
    print('cleared-----------');
    selectedTruckDetail = null;
    report = null;
    comment = null;
    fleetId = null;
    autoCompleteController.text = '';

    notifyListeners();
  }

  // setTruckListLoading(bool loading) async {
  //   // truckListLoading = loading;
  //   notifyListeners();
  // }
  postDamage(
    String? dam,
    String? com,
    String? fle,
  ) async {
    printLog('fleeeeeee---${fle}---1-----${com}----1-----${dam}---');
    showLoading();
    var body = DamageReportModel(message: dam, comments: com, fleetId: fle);
    printLog('--------${body.toJson()}');
    var result = await Services.postDamageReport(damageReport: body);
    if (result is Success) {
      showSnackBar(" trailer damage request send successfully!");
      clearAll();
      refresh();
    }
    if (result is Failure) {
      printLog('New trailer damage - ${result.response}');
      showSnackBar(
        "Failed to send trailer damage",
        success: false,
      );
    }
    clearAll();

    refresh();
  }

  init() {}
}

class FleetStatus {
  const FleetStatus(this.id, this.name);

  final String id;
  final String name;
}
