import 'package:image_picker/image_picker.dart';
import 'package:spot_on/utils/app_logger.dart';
import 'package:spot_on/utils/imports.dart';

import '../services/api.dart';

class TrailerStandUploadState extends ChangeNotifier {
  var api = Api();

  bool loading = false;
  bool navigateBack = false;

  bool proceed = false;

  List<XFile> imageList = [];

  void addImages(List<XFile> images) {
    imageList.clear();
    imageList.addAll(images);
    notifyListeners();
  }

  void clearImages() {
    imageList.clear();
    proceed = false;
    notifyListeners();
  }

  void trailerstandAllowToProceed() async {
    proceed = true;
    notifyListeners();
  }

  Future<void> _navBack() async {
    navigateBack = true;
    notifyListeners();
    await Future.delayed(const Duration(seconds: 2));
    navigateBack = false;
    notifyListeners();
  }

  void reset() {
    navigateBack = false;
    loading = false;
    notifyListeners();
  }

  void uploadImages({
    required List<XFile> images,
    required String jobId,
    required String reason,
  }) async {
    try {
      loading = true;
      notifyListeners();
      String response = await Api().uploadTrailerStand(
        images: images,
        jobId: jobId,
        reason: reason,
      );
      printLog(response);
      if (response == "Success") {
        _navBack();
        // showToast("${isUnsigned ? "New" : "Completed"} BoL uploaded!");
        // showSnackBar(
        //   "${isUnsigned ? "Unsigned" : "Signed"} BoL uploaded!",
        //   success: true,
        // );
      }
      loading = false;
      notifyListeners();
    } catch (e) {
      printLog(e);
      showSnackBar(
        "Failed to upload trailer stand!",
        success: true,
      );
      loading = false;
      notifyListeners();
    }
  }


   void uploadImageswithoutReason({
    required List<XFile> images,
    required String jobId,
    required String reason,
  }) async {
    try {
      loading = true;
      notifyListeners();
      String response = await Api().uploadTrailerStand(
        images: images,
        jobId: jobId,
        reason: reason,
      );
      printLog(response);
      if (response == "Success") {
        _navBack();
        // showToast("${isUnsigned ? "New" : "Completed"} BoL uploaded!");
        // showSnackBar(
        //   "${isUnsigned ? "Unsigned" : "Signed"} BoL uploaded!",
        //   success: true,
        // );
      }
      loading = false;
      notifyListeners();
    } catch (e) {
      printLog(e);
      showSnackBar(
        "Failed to upload trailer stand!",
        success: true,
      );
      loading = false;
      notifyListeners();
    }
  }
}
