import 'dart:io';

import 'package:spot_on/models/profile_update_model.dart';
import 'package:spot_on/models/user_info_model.dart';
import 'package:spot_on/screens/entry_exit_screen.dart';
import 'package:spot_on/screens/tabs/account_tab.dart';
import 'package:spot_on/screens/tabs/home_tab.dart';
import 'package:spot_on/screens/tabs/my_jobs_tab.dart';
import 'package:spot_on/screens/trailer_audit_screen.dart';
import 'package:spot_on/utils/imports.dart';
import 'package:spot_on/utils/push_notifications.dart';


class HomeState extends ChangeNotifier {
  ///
  int currentTabIndex = 0;
  bool userDetailsLoading = false;
  bool profileEditMode = false;
  bool profileUpdating = false;
  UserInfoModel? userInfoModel;
  ProfileUpdateModel profileUpdateModel = ProfileUpdateModel(
    email: '',
    firstName: '',
    lastName: '',
    newPassword: '',
    phone: '',
  );

  List dashboardTabs = [
    const HomeTab(),
    const MyJobsTab(),
    const AccountTab(),
  ];

  void refresh() async {
    notifyListeners();
  }

  setProfileUpdating(bool loading) async {
    profileUpdating = loading;
    notifyListeners();
  }

  setUserDetailsLoading(bool loading) async {
    userDetailsLoading = loading;
    notifyListeners();
  }

  setDashboardTabs(String role) {
    // String r = role.toLowerCase();
    if (isGuard()&& isTrailerAuditEnabled()) {
      dashboardTabs = [
        const EntryExitTab(), const MyJobsTab(), const TrailerAuditScreen(),
        //commented--

        // const DamageScreen(),
        const AccountTab()
      ];
      return;
    }
    if (isGuard()) {
      dashboardTabs = [
        const EntryExitTab(), const MyJobsTab(), 
        //commented--

        // const DamageScreen(),
        const AccountTab()
      ];
      return;
    }
    if (isClient()) {
      dashboardTabs = [
        const MyJobsTab(), const TrailerAuditScreen(), 
        //commented--

        // const DamageScreen(),
        const AccountTab()
      ];
      return;
    }
    if ((isDriver() || isSpotter()) && isTrailerAuditEnabled()) {
      dashboardTabs = [
        const HomeTab(),
        const MyJobsTab(),
        const TrailerAuditScreen(),
        //commented--

        // const DamageScreen(),
        const AccountTab(),
      ];
      return;
    }
    dashboardTabs = [
      const HomeTab(), const MyJobsTab(),
      //commented--
      // const DamageScreen(),
      const AccountTab()
    ];
  }

  Future<bool> askPushPermission() async {
    return await FBPushNotification.requestPushPermissions();
  }

  registerDevice() async {
    Services.registerDevice(
      deviceModel: '',
      deviceName: '',
      deviceRegistrationId: await FBPushNotification.getToken(),
      deviceType: Platform.isIOS ? 'IPHONE' : 'ANDROID',
    );
    await Future.delayed(const Duration(seconds: 2));
    Services.registerDeviceNew(
      deviceModel: '',
      deviceName: '',
      deviceRegistrationId: await FBPushNotification.getToken(),
      deviceType: Platform.isIOS ? 'IPHONE' : 'ANDROID',
    );
  }

  getUserInfo() async {
    setUserDetailsLoading(true);
    String userId = Preferences.loginResponseModel!.userId;
    var result = await Services.getUserInfo(userId: userId);
    if (result is Success) {
      userInfoModel = result.response as UserInfoModel;
      notifyListeners();
    }
    if (result is Failure) {
      // print(result.response);
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
    }
    setUserDetailsLoading(false);
  }

  updateUserInfo() async {
    setProfileUpdating(true);
    var result = await Services.updateProfile(profileUpdateModel);
    if (result is Success) {
      notifyListeners();
      getUserInfo();
      showSnackBar('Profile Updated Successfully');
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
      showSnackBar('Profile Updated Failed. Please try again.');
    }
    setProfileUpdating(false);
  }
}
