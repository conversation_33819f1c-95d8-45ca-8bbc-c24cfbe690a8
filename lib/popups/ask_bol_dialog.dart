import 'package:flutter/material.dart';
import 'package:spot_on/popups/confirm_bol_dialog.dart';
import 'package:spot_on/widgets/bol_upload.dart';

class AskBolDialog extends StatefulWidget {
  const AskBolDialog({
    Key? key,
    required this.jobId,
    required this.isUnsigned,
  }) : super(key: key);

  final String jobId;
  final bool isUnsigned;

  @override
  _AskBolDialogState createState() => _AskBolDialogState();
}

class _AskBolDialogState extends State<AskBolDialog> {
  bool? _selectedOption; // To track the selected radio option

  @override
  void initState() {
    super.initState();
    _selectedOption = true; // Set the initial selected option
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (BuildContext context, void Function(void Function()) setState) {
        return Dialog(
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(16))),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 24,
            ),
            // Add padding to the whole dialog
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Have ${widget.isUnsigned ? "a new" : "a completed"} BoL?',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Spacer(),
                    Radio(
                      fillColor: MaterialStateProperty.all(Colors.red),
                      value: true,
                      groupValue: _selectedOption,
                      onChanged: (bool? value) {
                        setState(() {
                          _selectedOption = value;
                        });
                      },
                    ),
                    const Text('Yes'),
                    const Spacer(),
                    Radio(
                      fillColor: MaterialStateProperty.all(Colors.red),
                      value: false,
                      groupValue: _selectedOption,
                      onChanged: (bool? value) {
                        setState(() {
                          _selectedOption = value;
                        });
                      },
                    ),
                    const Text('No'),
                    const Spacer(),
                  ],
                ),
                const SizedBox(height: 16),
                // Add some space between radio buttons and the submit button
                SizedBox(
                  width: double.infinity,
                  height: 40,
                  child: ElevatedButton(
                    onPressed: () {
                      if (_selectedOption == false) {
                        Navigator.of(context).pop(); // Close the dialog
                        showDialog(
                          context: context,
                          builder: (context) {
                            return ConfirmBolDialog(
                              isUnsigned: widget.isUnsigned,
                              jobId: widget.jobId,
                            );
                          },
                          barrierDismissible: false,
                        );
                        return;
                      }
                      Navigator.of(context).pop(); // Close the dialog
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => BolUpload(
                            isUnsigned: widget.isUnsigned,
                            jobId: widget.jobId,
                          ),
                          // fullscreenDialog: true,
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32), // Rounded corners
                      ),
                    ),
                    child: const Text(
                      'CONFIRM',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
