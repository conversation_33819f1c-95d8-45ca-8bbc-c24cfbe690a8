// To parse this JSON data, do
//
//     final loginResponseModel = loginResponseModelFromJson(jsonString);

import 'dart:convert';

LoginResponseModel loginResponseModelFromJson(String str) => LoginResponseModel.fromJson(json.decode(str));

String loginResponseModelToJson(LoginResponseModel data) => json.encode(data.toJson());

class LoginResponseModel {
  LoginResponseModel({
    required this.accessToken,
    required this.tokenType,
    required this.refreshToken,
    required this.expiresIn,
    required this.scope,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.roles,
    required this.timeZone,
    required this.userId,
    required this.email,
  });

  final String accessToken;
  final String tokenType;
  final String refreshToken;
  final int expiresIn;
  final String scope;
  final String firstName;
  final String lastName;
  final String phone;
  final List<String> roles;
  final String timeZone;
  final String userId;
  final String email;

  factory LoginResponseModel.fromJson(Map<String, dynamic> json) => LoginResponseModel(
        accessToken: json["access_token"],
        tokenType: json["token_type"],
        refreshToken: json["refresh_token"],
        expiresIn: json["expires_in"],
        scope: json["scope"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        phone: json["phone"],
        roles: json["roles"] == null ? [] : List<String>.from(json["roles"].map((x) => x)),
        timeZone: json["timeZone"],
        userId: json["userId"],
        email: json["email"],
      );

  Map<String, dynamic> toJson() => {
        "access_token": accessToken,
        "token_type": tokenType,
        "refresh_token": refreshToken,
        "expires_in": expiresIn,
        "scope": scope,
        "firstName": firstName,
        "lastName": lastName,
        "phone": phone,
        "roles": roles == null ? null : List<dynamic>.from(roles.map((x) => x)),
        "timeZone": timeZone,
        "userId": userId,
        "email": email,
      };
}
