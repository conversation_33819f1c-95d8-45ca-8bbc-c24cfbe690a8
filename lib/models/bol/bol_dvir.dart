// To parse this JSON data, do
//
//     final bolDvir = bolDvirFromJson(jsonString);

import 'dart:convert';

BolDvir bolDvirFromJson(String str) => BolDvir.fromJson(json.decode(str));

String bolDvirToJson(BolDvir data) => json.encode(data.toJson());

class BolDvir {
  bool? bol;
  bool? dvir;
  bool? trailerAudit;

  BolDvir({
    this.bol,
    this.dvir,
    this.trailerAudit,
  });

  factory BolDvir.fromJson(Map<String, dynamic> json) => BolDvir(
        bol: json["bol"],
        dvir: json["dvir"],
        trailerAudit: json["trailerAudit"],
      );

  Map<String, dynamic> toJson() => {
        "bol": bol,
        "dvir": dvir,
        "trailerAudit": trailerAudit,
      };
}
