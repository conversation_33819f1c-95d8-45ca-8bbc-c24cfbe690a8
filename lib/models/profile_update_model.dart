// To parse this JSON data, do
//
//     final profileUpdateModel = profileUpdateModelFromJson(jsonString);

import 'dart:convert';

ProfileUpdateModel profileUpdateModelFromJson(String str) => ProfileUpdateModel.fromJson(json.decode(str));

String profileUpdateModelToJson(ProfileUpdateModel data) => json.encode(data.toJson());

class ProfileUpdateModel {
  ProfileUpdateModel({
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.newPassword,
    required this.phone,
  });

  String email;
  String firstName;
  String lastName;
  String newPassword;
  String phone;

  factory ProfileUpdateModel.fromJson(Map<String, dynamic> json) => ProfileUpdateModel(
        email: json["email"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        newPassword: json["newPassword"],
        phone: json["phone"],
      );

  Map<String, dynamic> toJson() => {
        "email": email,
        "firstName": firstName,
        "lastName": lastName,
        "newPassword": newPassword,
        "phone": phone,
      };
}
