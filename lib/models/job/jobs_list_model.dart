// To parse this JSON data, do
//
//     final jobsListModel = jobsListModelFromJson(jsonString);

import 'dart:convert';

JobsListModel jobsListModelFromJson(String str) => JobsListModel.fromJson(json.decode(str));

String jobsListModelToJson(JobsListModel data) => json.encode(data.toJson());

class JobsListModel {
  JobsListModel({
    this.list = const [],
    this.page = 0,
    this.size = 0,
    this.totalElements = 0,
  });

  List<Job>? list;
  final int? page;
  final int? size;
  final int? totalElements;

  factory JobsListModel.fromJson(Map<String, dynamic> json) => JobsListModel(
        list: json["list"] == null ? [] : List<Job>.from(json["list"].map((x) => Job.fromJson(x))),
        page: json["page"] ?? 0,
        size: json["size"] ?? 0,
        totalElements: json["totalElements"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "list": list == null ? [] : List<dynamic>.from(list!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class Job {
  Job({
    this.sequenceAsn,
    this.assignedTo,
    this.audit,
    this.description,
    this.dropDateTime,
    this.dropLocation,
    this.dropNotes,
    this.dropSpot,
    this.fleet,
    this.fleetStatus,
    this.jobId,
    this.jobNumber,
    this.pickupDateTime,
    this.pickupLocation,
    this.pickupNotes,
    this.pickupSpot,
    this.priority,
    this.status,
    this.jobUpdateStatus,
    this.bols,
  });
  final String? sequenceAsn;
  final AssignedTo? assignedTo;
  final Audit? audit;
  final String? description;
  final String? dropDateTime;
  PLocation? dropLocation;
  final String? dropNotes;
  PSpot? dropSpot;
  final Fleet? fleet;
  String? fleetStatus;
  final String? jobId;
  final String? jobNumber;
  final String? pickupDateTime;
  final PLocation? pickupLocation;
  final String? pickupNotes;
  PSpot? pickupSpot;
  final String? priority;
  final String? status;
  String? jobUpdateStatus;
  List<Bol>? bols;

  factory Job.fromJson(Map<String, dynamic> json) => Job(
        sequenceAsn: json["sequenceAsn"],
        assignedTo: json["assignedTo"] == null ? null : AssignedTo.fromJson(json["assignedTo"]),
        audit: json["audit"] == null ? Audit() : Audit.fromJson(json["audit"]),
        description: json["description"],
        dropDateTime: json["dropDateTime"],
        dropLocation: null == json["dropLocation"] ? null : PLocation.fromJson(json["dropLocation"]),
        dropNotes: json["dropNotes"],
        jobNumber: json["jobNumber"],
        dropSpot: json["dropSpot"] == null ? null : PSpot.fromJson(json["dropSpot"]),
        fleet: json["fleet"] == null ? null : Fleet.fromJson(json["fleet"]),
        fleetStatus: json["fleetStatus"] ?? '',
        jobId: json["jobId"],
        pickupDateTime: json["pickupDateTime"],
        pickupLocation: json["pickupLocation"] == null ? null : PLocation.fromJson(json["pickupLocation"]),
        pickupNotes: json["pickupNotes"],
        pickupSpot: json["pickupSpot"] == null ? null : PSpot.fromJson(json["pickupSpot"]),
        priority: json["priority"],
        status: json["status"],
        bols: json["bols"] == null
            ? []
            : List<Bol>.from(
                json["bols"]!.map((x) => Bol.fromJson(x)),
              ),
      );

  Map<String, dynamic> toJson() => {
        "assignedTo": assignedTo == null ? null : assignedTo?.toJson(),
        "audit": audit == null ? null : audit?.toJson(),
        "description": description,
        "dropDateTime": dropDateTime,
        "jobNumber": jobNumber,
        "dropLocation": dropLocation == null ? null : dropLocation?.toJson(),
        "dropNotes": dropNotes,
        "dropSpot": dropSpot == null ? null : dropSpot?.toJson(),
        "fleet": fleet == null ? null : fleet?.toJson(),
        "fleetStatus": fleetStatus,
        "jobId": jobId,
        "pickupDateTime": pickupDateTime,
        "pickupLocation": pickupLocation == null ? null : pickupLocation?.toJson(),
        "pickupNotes": pickupNotes,
        "pickupSpot": pickupSpot == null ? null : pickupSpot?.toJson(),
        "priority": priority,
        "status": status,
        "bols": bols == null
            ? []
            : List<dynamic>.from(
                bols!.map((x) => x.toJson()),
              ),
      };
}

class AssignedTo {
  AssignedTo({
    this.clients = const [],
    this.email,
    this.firstName,
    this.isActive = false,
    this.lastLoginTime,
    this.lastName,
    this.phone,
    this.roles = const [],
    this.timeZone,
    this.userId,
  });

  final List<Client> clients;
  final String? email;
  final String? firstName;
  final bool isActive;
  final String? lastLoginTime;
  final String? lastName;
  final String? phone;
  final List<Role> roles;
  final String? timeZone;
  final String? userId;

  factory AssignedTo.fromJson(Map<String, dynamic> json) => AssignedTo(
        clients: json["clients"] == null ? [] : List<Client>.from(json["clients"].map((x) => Client.fromJson(x))),
        email: json["email"],
        firstName: json["firstName"],
        isActive: json["isActive"],
        lastLoginTime: json["lastLoginTime"],
        lastName: json["lastName"],
        phone: json["phone"],
        roles: json["roles"] == null ? [] : List<Role>.from(json["roles"].map((x) => Role.fromJson(x))),
        timeZone: json["timeZone"],
        userId: json["userId"],
      );

  Map<String, dynamic> toJson() => {
        "clients": clients == null ? null : List<dynamic>.from(clients.map((x) => x.toJson())),
        "email": email,
        "firstName": firstName,
        "isActive": isActive,
        "lastLoginTime": lastLoginTime,
        "lastName": lastName,
        "phone": phone,
        "roles": roles == null ? null : List<dynamic>.from(roles.map((x) => x.toJson())),
        "timeZone": timeZone,
        "userId": userId,
      };
}

class Client {
  Client({
    this.city,
    this.clientId,
    this.clientName,
    this.contactEmail,
    this.contactPerson,
    this.contactPhone,
    this.country,
    this.isActive = false,
    this.remarks,
    this.state,
    this.street,
    this.zip,
  });

  final String? city;
  final String? clientId;
  final String? clientName;
  final String? contactEmail;
  final String? contactPerson;
  final String? contactPhone;
  final String? country;
  final bool isActive;
  final String? remarks;
  final String? state;
  final String? street;
  final String? zip;

  factory Client.fromJson(Map<String, dynamic> json) => Client(
        city: json["city"],
        clientId: json["clientId"],
        clientName: json["clientName"],
        contactEmail: json["contactEmail"],
        contactPerson: json["contactPerson"],
        contactPhone: json["contactPhone"],
        country: json["country"],
        isActive: json["isActive"],
        remarks: json["remarks"],
        state: json["state"],
        street: json["street"],
        zip: json["zip"],
      );

  Map<String, dynamic> toJson() => {
        "city": city,
        "clientId": clientId,
        "clientName": clientName,
        "contactEmail": contactEmail,
        "contactPerson": contactPerson,
        "contactPhone": contactPhone,
        "country": country,
        "isActive": isActive,
        "remarks": remarks,
        "state": state,
        "street": street,
        "zip": zip,
      };
}

class Role {
  Role({
    this.isActive = false,
    this.roleId,
    this.roleName,
  });

  final bool isActive;
  final String? roleId;
  final String? roleName;

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        isActive: json["isActive"],
        roleId: json["roleId"],
        roleName: json["roleName"],
      );

  Map<String, dynamic> toJson() => {
        "isActive": isActive,
        "roleId": roleId,
        "roleName": roleName,
      };
}

class Audit {
  Audit({
    this.createdBy,
    this.createdDate,
    this.lastModifiedBy,
    this.lastModifiedDate,
  });

  final AssignedTo? createdBy;
  final String? createdDate;
  final AssignedTo? lastModifiedBy;
  final String? lastModifiedDate;

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdBy: json["createdBy"] == null ? null : AssignedTo.fromJson(json["createdBy"]),
        createdDate: json["createdDate"],
        lastModifiedBy: json["lastModifiedBy"] == null ? null : AssignedTo.fromJson(json["lastModifiedBy"]),
        lastModifiedDate: json["lastModifiedDate"],
      );

  Map<String, dynamic> toJson() => {
        "createdBy": createdBy,
        "createdDate": createdDate,
        "lastModifiedBy": lastModifiedBy,
        "lastModifiedDate": lastModifiedDate,
      };
}

class PLocation {
  PLocation({
    this.audit,
    this.city,
    this.clientId,
    this.country,
    this.isActive = false,
    this.latitude = 0,
    this.locationId,
    this.locationName,
    this.longitude = 0,
    this.remarks,
    this.state,
    this.street,
    this.zip,
  });

  final Audit? audit;
  final String? city;
  final String? clientId;
  final String? country;
  final bool isActive;
  final double? latitude;
  String? locationId;
  final String? locationName;
  final double? longitude;
  final String? remarks;
  final String? state;
  final String? street;
  final String? zip;

  factory PLocation.fromJson(Map<String, dynamic> json) => PLocation(
        // audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
        city: json["city"],
        clientId: json["clientId"],
        country: json["country"],
        isActive: json["isActive"],
        latitude: json["latitude"] ?? 0,
        locationId: json["locationId"],
        locationName: json["locationName"],
        longitude: json["longitude"],
        remarks: json["remarks"],
        state: json["state"],
        street: json["street"],
        zip: json["zip"],
      );

  Map<String, dynamic> toJson() => {
        // "audit": audit == null ? null : audit?.toJson(),
        "city": city,
        "clientId": clientId,
        "country": country,
        "isActive": isActive,
        "latitude": latitude,
        "locationId": locationId,
        "locationName": locationName,
        "longitude": longitude,
        "remarks": remarks,
        "state": state,
        "street": street,
        "zip": zip,
      };
}

class PSpot {
  PSpot({
    this.audit,
    this.isActive = false,
    this.latitude,
    this.locationId,
    this.longitude,
    this.remarks,
    this.spotId,
    this.spotName,
    this.status,
    this.type,
  });

  final Audit? audit;
  final bool isActive;
  final double? latitude;
  final String? locationId;
  final double? longitude;
  final String? remarks;
  String? spotId;
  final String? spotName;
  final String? status;
  final String? type;

  factory PSpot.fromJson(Map<String, dynamic> json) => PSpot(
        audit: json["audit"] == null ? Audit() : Audit.fromJson(json["audit"]),
        isActive: json["isActive"] ?? false,
        latitude: json["latitude"] ?? 0,
        locationId: json["locationId"],
        longitude: json["longitude"] ?? 0,
        remarks: json["remarks"],
        spotId: json["spotId"],
        spotName: json["spotName"],
        status: json["status"],
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "audit": audit == null ? null : audit?.toJson(),
        "isActive": isActive,
        "latitude": latitude,
        "locationId": locationId,
        "longitude": longitude,
        "remarks": remarks,
        "spotId": spotId,
        "spotName": spotName,
        "status": status,
        "type": type,
      };
}

class Fleet {
  Fleet({
    this.audit,
    this.carrier,
    this.color,
    this.fleetId,
    this.isActive = false,
    this.make,
    this.model,
    this.plateNumber,
    this.unitNumber,
    this.remarks,
    this.type,
    this.year,
  });

  final Audit? audit;
  final String? carrier;
  final String? color;
  final String? fleetId;
  final bool isActive;
  final String? make;
  final String? model;
  final String? plateNumber;
  final String? unitNumber;
  final String? remarks;
  final String? type;
  final int? year;

  factory Fleet.fromJson(Map<String, dynamic> json) => Fleet(
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
        carrier: json["carrier"],
        color: json["color"],
        fleetId: json["fleetId"],
        isActive: json["isActive"],
        make: json["make"],
        model: json["model"],
        plateNumber: json["plateNumber"],
        unitNumber: json["unitNumber"],
        remarks: json["remarks"],
        type: json["type"],
        year: json["year"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "audit": audit == null ? null : audit?.toJson(),
        "carrier": carrier,
        "color": color,
        "fleetId": fleetId,
        "isActive": isActive,
        "make": make,
        "model": model,
        "plateNumber": plateNumber,
        "remarks": remarks,
        "type": type,
        "year": year ?? 0,
      };
}

class Bol {
  String? imagePath;

  Bol({
    this.imagePath,
  });

  factory Bol.fromJson(Map<String, dynamic> json) => Bol(
        imagePath: json["imagePath"],
      );

  Map<String, dynamic> toJson() => {
        "imagePath": imagePath,
      };
}
