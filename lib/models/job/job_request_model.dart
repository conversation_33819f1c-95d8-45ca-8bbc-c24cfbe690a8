// To parse this JSON data, do
//
//     final jobRequestModel = jobRequestModelFromJson(jsonString);

import 'dart:convert';

JobRequestModel jobRequestModelFromJson(String str) => JobRequestModel.fromJson(json.decode(str));

String jobRequestModelToJson(JobRequestModel data) => json.encode(data.toJson());

class JobRequestModel {
  JobRequestModel({
    required this.bucket,
    //----commented-bucket

    required this.assignedToUserId,
    required this.description,
    required this.dropLocationId,
    required this.dropSpotId,
    required this.fleetId,
    required this.fleetStatus,
    required this.pickupLocationId,
    required this.pickupSpotId,
    required this.priority,
  });
  String bucket;
  //----commented-bucket

  String assignedToUserId;
  String description;
  String dropLocationId;
  String? dropSpotId;
  String fleetId;
  String? fleetStatus;
  String pickupLocationId;
  String? pickupSpotId;
  String priority;

  factory JobRequestModel.fromJson(Map<String, dynamic> json) => JobRequestModel(
        assignedToUserId: json["assignedToUserId"],
        bucket: json["bucket"],
        //----commented-bucket

        description: json["description"],
        dropLocationId: json["dropLocationId"],
        dropSpotId: json["dropSpotId"],
        fleetId: json["fleetId"],
        fleetStatus: json["fleetStatus"],
        pickupLocationId: json["pickupLocationId"],
        pickupSpotId: json["pickupSpotId"],
        priority: json["priority"],
      );

  Map<String, dynamic> toJson() => {
        "bucket": bucket,
        //----commented-bucket

        "assignedToUserId": assignedToUserId,
        "description": description ?? '',
        "dropLocationId": dropLocationId,
        "dropSpotId": dropSpotId,
        "fleetId": fleetId,
        "fleetStatus": fleetStatus,
        "pickupLocationId": pickupLocationId,
        "pickupSpotId": pickupSpotId,
        "priority": priority,
      };
}
