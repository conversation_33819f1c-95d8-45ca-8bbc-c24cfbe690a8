// To parse this JSON data, do
//
//     final usersListeEntryExit = usersListeEntryExitFromJson(jsonString);

import 'dart:convert';

UsersListeEntryExit usersListeEntryExitFromJson(String str) =>
    UsersListeEntryExit.fromJson(json.decode(str));

String usersListeEntryExitToJson(UsersListeEntryExit data) =>
    json.encode(data.toJson());

class UsersListeEntryExit {
  final List<UsersListElement>? list;
  final num? page;
  final num? size;
  final num? totalElements;

  UsersListeEntryExit({
    this.list,
    this.page,
    this.size,
    this.totalElements,
  });

  UsersListeEntryExit copyWith({
    List<UsersListElement>? list,
    num? page,
    num? size,
    num? totalElements,
  }) =>
      UsersListeEntryExit(
        list: list ?? this.list,
        page: page ?? this.page,
        size: size ?? this.size,
        totalElements: totalElements ?? this.totalElements,
      );

  factory UsersListeEntryExit.fromJson(Map<String, dynamic> json) =>
      UsersListeEntryExit(
        list: json["list"] == null
            ? []
            : List<UsersListElement>.from(
                json["list"]!.map((x) => UsersListElement.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": list == null
            ? []
            : List<dynamic>.from(list!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class UsersListElement {
  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? timeZone;
  final String? lastLoginTime;
  final bool? isActive;
  final bool? isAdUser;
  final List<Role>? roles;
  final List<Client>? clients;
  final bool? isOnOverTime;
  final DateTime? idleSince;
  final num? idleTime;
  final String? phone;

  UsersListElement({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.timeZone,
    this.lastLoginTime,
    this.isActive,
    this.isAdUser,
    this.roles,
    this.clients,
    this.isOnOverTime,
    this.idleSince,
    this.idleTime,
    this.phone,
  });

  UsersListElement copyWith({
    String? userId,
    String? firstName,
    String? lastName,
    String? email,
    String? timeZone,
    String? lastLoginTime,
    bool? isActive,
    bool? isAdUser,
    List<Role>? roles,
    List<Client>? clients,
    bool? isOnOverTime,
    DateTime? idleSince,
    num? idleTime,
    String? phone,
  }) =>
      UsersListElement(
        userId: userId ?? this.userId,
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
        email: email ?? this.email,
        timeZone: timeZone ?? this.timeZone,
        lastLoginTime: lastLoginTime ?? this.lastLoginTime,
        isActive: isActive ?? this.isActive,
        isAdUser: isAdUser ?? this.isAdUser,
        roles: roles ?? this.roles,
        clients: clients ?? this.clients,
        isOnOverTime: isOnOverTime ?? this.isOnOverTime,
        idleSince: idleSince ?? this.idleSince,
        idleTime: idleTime ?? this.idleTime,
        phone: phone ?? this.phone,
      );

  factory UsersListElement.fromJson(Map<String, dynamic> json) =>
      UsersListElement(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
        isActive: json["isActive"],
        isAdUser: json["isADUser"],
        roles: json["roles"] == null
            ? []
            : List<Role>.from(json["roles"]!.map((x) => Role.fromJson(x))),
        clients: json["clients"] == null
            ? []
            : List<Client>.from(
                json["clients"]!.map((x) => Client.fromJson(x))),
        isOnOverTime: json["isOnOverTime"],
        idleSince: json["idleSince"] == null
            ? null
            : DateTime.parse(json["idleSince"]),
        idleTime: json["idleTime"],
        phone: json["phone"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
        "isActive": isActive,
        "isADUser": isAdUser,
        "roles": roles == null
            ? []
            : List<dynamic>.from(roles!.map((x) => x.toJson())),
        "clients": clients == null
            ? []
            : List<dynamic>.from(clients!.map((x) => x.toJson())),
        "isOnOverTime": isOnOverTime,
        "idleSince": idleSince?.toIso8601String(),
        "idleTime": idleTime,
        "phone": phone,
      };
}

class Client {
  final String? clientId;
  final String? clientName;
  final String? street;
  final String? city;
  final String? state;
  final String? zip;
  final String? country;
  final String? contactPerson;
  final String? contactEmail;
  final String? contactPhone;
  final bool? isActive;
  final bool? dvir;
  final bool? bol;
  final bool? accountDeactivation;
  final bool? trailerAudit;
  final Audit? audit;
  final String? timeZone;
  final num? overTime;

  Client({
    this.clientId,
    this.clientName,
    this.street,
    this.city,
    this.state,
    this.zip,
    this.country,
    this.contactPerson,
    this.contactEmail,
    this.contactPhone,
    this.isActive,
    this.dvir,
    this.bol,
    this.accountDeactivation,
    this.trailerAudit,
    this.audit,
    this.timeZone,
    this.overTime,
  });

  Client copyWith({
    String? clientId,
    String? clientName,
    String? street,
    String? city,
    String? state,
    String? zip,
    String? country,
    String? contactPerson,
    String? contactEmail,
    String? contactPhone,
    bool? isActive,
    bool? dvir,
    bool? bol,
    bool? accountDeactivation,
    bool? trailerAudit,
    Audit? audit,
    String? timeZone,
    num? overTime,
  }) =>
      Client(
        clientId: clientId ?? this.clientId,
        clientName: clientName ?? this.clientName,
        street: street ?? this.street,
        city: city ?? this.city,
        state: state ?? this.state,
        zip: zip ?? this.zip,
        country: country ?? this.country,
        contactPerson: contactPerson ?? this.contactPerson,
        contactEmail: contactEmail ?? this.contactEmail,
        contactPhone: contactPhone ?? this.contactPhone,
        isActive: isActive ?? this.isActive,
        dvir: dvir ?? this.dvir,
        bol: bol ?? this.bol,
        accountDeactivation: accountDeactivation ?? this.accountDeactivation,
        trailerAudit: trailerAudit ?? this.trailerAudit,
        audit: audit ?? this.audit,
        timeZone: timeZone ?? this.timeZone,
        overTime: overTime ?? this.overTime,
      );

  factory Client.fromJson(Map<String, dynamic> json) => Client(
        clientId: json["clientId"],
        clientName: json["clientName"],
        street: json["street"],
        city: json["city"],
        state: json["state"],
        zip: json["zip"],
        country: json["country"],
        contactPerson: json["contactPerson"],
        contactEmail: json["contactEmail"],
        contactPhone: json["contactPhone"],
        isActive: json["isActive"],
        dvir: json["dvir"],
        bol: json["bol"],
        accountDeactivation: json["accountDeactivation"],
        trailerAudit: json["trailerAudit"],
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
        timeZone: json["timeZone"],
        overTime: json["overTime"],
      );

  Map<String, dynamic> toJson() => {
        "clientId": clientId,
        "clientName": clientName,
        "street": street,
        "city": city,
        "state": state,
        "zip": zip,
        "country": country,
        "contactPerson": contactPerson,
        "contactEmail": contactEmail,
        "contactPhone": contactPhone,
        "isActive": isActive,
        "dvir": dvir,
        "bol": bol,
        "accountDeactivation": accountDeactivation,
        "trailerAudit": trailerAudit,
        "audit": audit?.toJson(),
        "timeZone": timeZone,
        "overTime": overTime,
      };
}

class Audit {
  final num? auditInterval;
  final num? auditDueDay;

  Audit({
    this.auditInterval,
    this.auditDueDay,
  });

  Audit copyWith({
    num? auditInterval,
    num? auditDueDay,
  }) =>
      Audit(
        auditInterval: auditInterval ?? this.auditInterval,
        auditDueDay: auditDueDay ?? this.auditDueDay,
      );

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        auditInterval: json["auditInterval"],
        auditDueDay: json["auditDueDay"],
      );

  Map<String, dynamic> toJson() => {
        "auditInterval": auditInterval,
        "auditDueDay": auditDueDay,
      };
}

class Role {
  final String? roleId;
  final String? roleName;
  final bool? isActive;

  Role({
    this.roleId,
    this.roleName,
    this.isActive,
  });

  Role copyWith({
    String? roleId,
    String? roleName,
    bool? isActive,
  }) =>
      Role(
        roleId: roleId ?? this.roleId,
        roleName: roleName ?? this.roleName,
        isActive: isActive ?? this.isActive,
      );

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        roleId: json["roleId"],
        roleName: json["roleName"],
        isActive: json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "roleId": roleId,
        "roleName": roleName,
        "isActive": isActive,
      };
}
