// // To parse this JSON data, do
// //
// // final carrierList = carrierListFromJson(jsonString);

// // import 'dart:convert';

// // List<String> carrierListFromJson(String str) => List<String>.from(json.decode(str).map((x) => x));

// // String carrierListToJson(List<String> data) => json.encode(List<dynamic>.from(data.map((x) => x)));
// // To parse this JSON data, do

//     // final carrierList = carrierListFromJson(jsonString);

// import 'dart:convert';

// CarrierList carrierListFromJson(String str) =>
//     CarrierList.fromJson(json.decode(str));

// String carrierListToJson(CarrierList data) => json.encode(data.toJson());

// class CarrierList {
//   final List<String>? list;
//   final int? page;
//   final int? size;
//   final int? totalElements;

//   CarrierList({
//     this.list,
//     this.page,
//     this.size,
//     this.totalElements,
//   });

//   CarrierList copyWith({
//     List<String>? list,
//     int? page,
//     int? size,
//     int? totalElements,
//   }) =>
//       CarrierList(
//         list: list ?? this.list,
//         page: page ?? this.page,
//         size: size ?? this.size,
//         totalElements: totalElements ?? this.totalElements,
//       );

//   factory CarrierList.fromJson(Map<String, dynamic> json) => CarrierList(
//         list: json["list"] == null
//             ? []
//             : List<String>.from(
//                 json["list"]!.map((x) => ListElement.fromJson(x))),
//         page: json["page"],
//         size: json["size"],
//         totalElements: json["totalElements"],
//       );

//   Map<String, dynamic> toJson() => {
//         "list": list == null
//             ? []
//             : List<dynamic>.from(list!.map((x) => x)),
//         "page": page,
//         "size": size,
//         "totalElements": totalElements,
//       };
// }

// class ListElement {
//   final String? carrierId;
//   final String? carrier;
//   final int? id;

//   ListElement({
//     this.carrierId,
//     this.carrier,
//     this.id,
//   });

//   ListElement copyWith({
//     String? carrierId,
//     String? carrier,
//     int? id,
//   }) =>
//       ListElement(
//         carrierId: carrierId ?? this.carrierId,
//         carrier: carrier ?? this.carrier,
//         id: id ?? this.id,
//       );

//   factory ListElement.fromJson(Map<String, dynamic> json) => ListElement(
//         carrierId: json["carrierId"],
//         carrier: json["carrier"],
//         id: json["id"],
//       );

//   Map<String, dynamic> toJson() => {
//         "carrierId": carrierId,
//         "carrier": carrier,
//         "id": id,
//       };
// }

// class Carrier {
//   final String carrierId;
//   final String carrier;
//   final int id;

//   Carrier({required this.carrierId, required this.carrier, required this.id});

//   factory Carrier.fromJson(Map<String, dynamic> json) {
//     return Carrier(
//       carrierId: json['carrierId'],
//       carrier: json['carrier'],
//       id: json['id'],
//     );
//   }
// }
// To parse this JSON data, do
//
//     final entryExitTrailerAutofillModel = entryExitTrailerAutofillModelFromJson(jsonString);

// To parse this JSON data, do
//
//     final carrierList = carrierListFromJson(jsonString);

import 'dart:convert';

List<String> carrierListFromJson(String str) => List<String>.from(json.decode(str).map((x) => x));

String carrierListToJson(List<String> data) => json.encode(List<dynamic>.from(data.map((x) => x)));
