// To parse this JSON data, do
//
//     final spotsList = spotsListFromJson(jsonString);

import 'dart:convert';

SpotsList spotsListFromJson(String str) => SpotsList.fromJson(json.decode(str));

String spotsListToJson(SpotsList data) => json.encode(data.toJson());

class SpotsList {
  SpotsList({
    this.list = const [],
    this.page = 0,
    this.size = 0,
    this.totalElements = 0,
  });

  final List<Spot> list;
  final int page;
  final int size;
  final int totalElements;

  factory SpotsList.fromJson(Map<String, dynamic> json) => SpotsList(
        list: json["list"] == null ? [] : List<Spot>.from(json["list"].map((x) => Spot.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": List<dynamic>.from(list.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class Spot {
  Spot({
    this.locationId,
    this.spotId,
    this.spotName,
    this.type,
    this.status,
    this.latitude,
    this.longitude,
    this.remarks,
    this.isActive,
    this.audit,
    this.fleet,
  });

  final String? locationId;
  final String? spotId;
  final String? spotName;
  final String? type;
  final String? status;
  final double? latitude;
  final double? longitude;
  final String? remarks;
  final bool? isActive;
  final Audit? audit;
  final Fleet? fleet;

  factory Spot.fromJson(Map<String, dynamic> json) => Spot(
        locationId: json["locationId"],
        spotId: json["spotId"],
        spotName: json["spotName"],
        type: json["type"],
        status: json["status"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        remarks: json["remarks"],
        isActive: json["isActive"],
        fleet: json["fleet"] == null ? null : Fleet.fromJson(json["fleet"]),
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
      );

  Map<String, dynamic> toJson() => {
        "locationId": locationId,
        "spotId": spotId,
        "spotName": spotName,
        "type": type,
        "status": status,
        "latitude": latitude,
        "longitude": longitude,
        "remarks": remarks,
        "isActive": isActive,
        "audit": audit?.toJson(),
        "fleet": fleet == null ? null : fleet?.toJson(),
      };
}

class Audit {
  Audit({
    this.createdDate,
    this.lastModifiedDate,
    this.createdBy,
    this.lastModifiedBy,
  });

  final String? createdDate;
  final String? lastModifiedDate;
  final EdBy? createdBy;
  final EdBy? lastModifiedBy;

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
        createdBy: json["createdBy"] == null ? null : EdBy.fromJson(json["createdBy"]),
        lastModifiedBy: json["lastModifiedBy"] == null ? null : EdBy.fromJson(json["lastModifiedBy"]),
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
        "createdBy": createdBy == null ? null : createdBy?.toJson(),
        "lastModifiedBy": lastModifiedBy == null ? null : lastModifiedBy?.toJson(),
      };
}

class EdBy {
  EdBy({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
  });

  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? timeZone;
  final String? lastLoginTime;

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
      };
}

class Fleet {
  Fleet({
    this.audit,
    this.carrier,
    this.fleetId,
    this.fleetStatus,
    this.isActive,
    this.isHotTrailer,
    this.owner,
    this.remarks,
    this.type,
    this.unitNumber,
  });

  final Audit? audit;
  final String? carrier;
  final String? fleetId;
  final String? fleetStatus;
  final bool? isActive;
  final bool? isHotTrailer;
  final String? owner;
  final String? remarks;
  final String? type;
  final String? unitNumber;

  factory Fleet.fromJson(Map<String, dynamic> json) => Fleet(
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
        carrier: json["carrier"],
        fleetId: json["fleetId"],
        fleetStatus: json["fleetStatus"],
        isActive: json["isActive"],
        isHotTrailer: json["isHotTrailer"],
        owner: json["owner"],
        remarks: json["remarks"],
        type: json["type"],
        unitNumber: json["unitNumber"],
      );

  Map<String, dynamic> toJson() => {
        "audit": audit == null ? null : audit?.toJson(),
        "carrier": carrier,
        "fleetId": fleetId,
        "fleetStatus": fleetStatus,
        "isActive": isActive,
        "isHotTrailer": isHotTrailer,
        "owner": owner,
        "remarks": remarks,
        "type": type,
        "unitNumber": unitNumber,
      };
}
