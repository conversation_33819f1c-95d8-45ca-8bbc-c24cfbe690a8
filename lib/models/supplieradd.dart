// To parse this JSON data, do
//
//     final supplierSuccess = supplierSuccessFromJson(jsonString);

import 'dart:convert';

SupplierSuccess supplierSuccessFromJson(String str) =>
    SupplierSuccess.fromJson(json.decode(str));

String supplierSuccessToJson(SupplierSuccess data) =>
    json.encode(data.toJson());

class SupplierSuccess {
  final String? supplierId;
  final String? supplier;
  final int? id;

  SupplierSuccess({
    this.supplierId,
    this.supplier,
    this.id,
  });

  SupplierSuccess copyWith({
    String? supplierId,
    String? supplier,
    int? id,
  }) =>
      SupplierSuccess(
        supplierId: supplierId ?? this.supplierId,
        supplier: supplier ?? this.supplier,
        id: id ?? this.id,
      );

  factory SupplierSuccess.fromJson(Map<String, dynamic> json) =>
      SupplierSuccess(
        supplierId: json["supplierId"],
        supplier: json["supplier"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "supplierId": supplierId,
        "supplier": supplier,
        "id": id,
      };
}
