// To parse this JSON data, do
//
//     final supplierList = supplierListFromJson(jsonString);

import 'dart:convert';

SupplierList supplierListFromJson(String str) =>
    SupplierList.fromJson(json.decode(str));

String supplierListToJson(SupplierList data) => json.encode(data.toJson());

class SupplierList {
  final List<ListSupplier>? list;
  final int? page;
  final int? size;
  final int? totalElements;

  SupplierList({
    this.list,
    this.page,
    this.size,
    this.totalElements,
  });

  SupplierList copyWith({
    List<ListSupplier>? list,
    int? page,
    int? size,
    int? totalElements,
  }) =>
      SupplierList(
        list: list ?? this.list,
        page: page ?? this.page,
        size: size ?? this.size,
        totalElements: totalElements ?? this.totalElements,
      );

  factory SupplierList.fromJson(Map<String, dynamic> json) => SupplierList(
        list: json["list"] == null
            ? []
            : List<ListSupplier>.from(
                json["list"]!.map((x) => ListSupplier.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": list == null
            ? []
            : List<dynamic>.from(list!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class ListSupplier {
  final String? supplierId;
  final String? supplier;
  final int? id;

  ListSupplier({
    this.supplierId,
    this.supplier,
    this.id,
  });

  ListSupplier copyWith({
    String? supplierId,
    String? supplier,
    int? id,
  }) =>
      ListSupplier(
        supplierId: supplierId ?? this.supplierId,
        supplier: supplier ?? this.supplier,
        id: id ?? this.id,
      );

  factory ListSupplier.fromJson(Map<String, dynamic> json) => ListSupplier(
        supplierId: json["supplierId"],
        supplier: json["supplier"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "supplierId": supplierId,
        "supplier": supplier,
        "id": id,
      };
}
