import 'dart:convert';

UserInfoModel userInfoModelFromJson(String str) => UserInfoModel.fromJson(json.decode(str));

String userInfoModelToJson(UserInfoModel data) => json.encode(data.toJson());

class UserInfoModel {
  UserInfoModel({
    this.clients = const [],
    this.email,
    this.firstName,
    this.isActive = false,
    this.lastLoginTime,
    this.lastName,
    this.phone,
    this.roles = const [],
    this.timeZone,
    this.userId,
  });

  final List<Client> clients;
  final String? email;
  final String? firstName;
  final bool isActive;
  final String? lastLoginTime;
  final String? lastName;
  final String? phone;
  final List<Role> roles;
  final String? timeZone;
  final String? userId;

  factory UserInfoModel.fromJson(Map<String, dynamic> json) => UserInfoModel(
        clients: json["clients"] == null ? [] : List<Client>.from(json["clients"].map((x) => Client.fromJson(x))),
        email: json["email"],
        firstName: json["firstName"],
        isActive: json["isActive"],
        lastLoginTime: json["lastLoginTime"],
        lastName: json["lastName"],
        phone: json["phone"],
        roles: json["roles"] == null ? [] : List<Role>.from(json["roles"].map((x) => Role.fromJson(x))),
        timeZone: json["timeZone"],
        userId: json["userId"],
      );

  Map<String, dynamic> toJson() => {
        "clients": clients == null ? null : List<dynamic>.from(clients.map((x) => x.toJson())),
        "email": email,
        "firstName": firstName,
        "isActive": isActive,
        "lastLoginTime": lastLoginTime,
        "lastName": lastName,
        "phone": phone,
        "roles": roles == null ? null : List<dynamic>.from(roles.map((x) => x.toJson())),
        "timeZone": timeZone,
        "userId": userId,
      };
}

class Client {
  Client({
    this.audit,
    this.city,
    this.clientId,
    this.clientName,
    this.contactEmail,
    this.contactPerson,
    this.contactPhone,
    this.country,
    this.isActive = false,
    this.remarks,
    this.state,
    this.street,
    this.zip,
    this.dvir,
    this.bol,
  });

  final Audit? audit;
  final String? city;
  final String? clientId;
  final String? clientName;
  final String? contactEmail;
  final String? contactPerson;
  final String? contactPhone;
  final String? country;
  final bool isActive;
  final String? remarks;
  final String? state;
  final String? street;
  final String? zip;
  final bool? dvir;
  final bool? bol;

  factory Client.fromJson(Map<String, dynamic> json) => Client(
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
        city: json["city"],
        clientId: json["clientId"],
        clientName: json["clientName"],
        contactEmail: json["contactEmail"],
        contactPerson: json["contactPerson"],
        contactPhone: json["contactPhone"],
        country: json["country"],
        isActive: json["isActive"],
        remarks: json["remarks"],
        state: json["state"],
        street: json["street"],
        zip: json["zip"],
        dvir: json["dvir"],
        bol: json["bol"],
      );

  Map<String, dynamic> toJson() => {
        "audit": audit == null ? null : audit?.toJson(),
        "city": city,
        "clientId": clientId,
        "clientName": clientName,
        "contactEmail": contactEmail,
        "contactPerson": contactPerson,
        "contactPhone": contactPhone,
        "country": country,
        "isActive": isActive,
        "remarks": remarks,
        "state": state,
        "street": street,
        "zip": zip,
      };
}

class Audit {
  Audit({
    this.createdDate,
    this.lastModifiedDate,
  });

  final String? createdDate;
  final String? lastModifiedDate;

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
      };
}

class Role {
  Role({
    this.isActive = false,
    this.roleId,
    this.roleName,
  });

  final bool isActive;
  final String? roleId;
  final String? roleName;

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        isActive: json["isActive"],
        roleId: json["roleId"],
        roleName: json["roleName"],
      );

  Map<String, dynamic> toJson() => {
        "isActive": isActive,
        "roleId": roleId,
        "roleName": roleName,
      };
}
