// To parse this JSON data, do
//
//     final forceUpdateModel = forceUpdateModelFromJson(jsonString);

import 'dart:convert';

ForceUpdateModel forceUpdateModelFromJson(String str) => ForceUpdateModel.fromJson(json.decode(str));

String forceUpdateModelToJson(ForceUpdateModel data) => json.encode(data.toJson());

class ForceUpdateModel {
  bool? forceUpdate;
  bool? warning;

  ForceUpdateModel({
    this.forceUpdate,
    this.warning,
  });

  factory ForceUpdateModel.fromJson(Map<String, dynamic> json) => ForceUpdateModel(
        forceUpdate: json["forceUpdate"],
        warning: json["warning"],
      );

  Map<String, dynamic> toJson() => {
        "forceUpdate": forceUpdate,
        "warning": warning,
      };
}
