// To parse this JSON data, do
//
//     final carrierSuccess = carrierSuccessFromJson(jsonString);

import 'dart:convert';

CarrierSuccess carrierSuccessFromJson(String str) =>
    CarrierSuccess.fromJson(json.decode(str));

String carrierSuccessToJson(CarrierSuccess data) => json.encode(data.toJson());

class CarrierSuccess {
  final String? carrierId;
  final String? carrier;
  final int? id;

  CarrierSuccess({
    this.carrierId,
    this.carrier,
    this.id,
  });

  CarrierSuccess copyWith({
    String? carrierId,
    String? carrier,
    int? id,
  }) =>
      CarrierSuccess(
        carrierId: carrierId ?? this.carrierId,
        carrier: carrier ?? this.carrier,
        id: id ?? this.id,
      );

  factory CarrierSuccess.fromJson(Map<String, dynamic> json) => CarrierSuccess(
        carrierId: json["carrierId"],
        carrier: json["carrier"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "carrierId": carrierId,
        "carrier": carrier,
        "id": id,
      };
}
