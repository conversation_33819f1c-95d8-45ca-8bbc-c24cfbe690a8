import 'dart:convert';

DamageReportModel damageReportFromJson(String str) => DamageReportModel.fromJson(json.decode(str));

String damageReportToJson(DamageReportModel data) => json.encode(data.toJson());

class DamageReportModel {
  String? comments;
  String? message;
  String? fleetId;
  // String? note;

  DamageReportModel({
    this.comments,
    this.message,
    this.fleetId,
    // this.note
  });

  factory DamageReportModel.fromJson(Map<String, dynamic> json) => DamageReportModel(
        comments: json["comments"],
        message: json["message"],
        fleetId: json["fleetId"],
        // note: json["note"],
      );

  Map<String, dynamic> toJson() => {
        "comments": comments,
        "message": message,
        "fleetId": fleetId,
        // "note": note,
      };
}
