// To parse this JSON data, do
//
//     final newTrailerReqModel = newTrailerReqModelFromJson(jsonString);

import 'dart:convert';

NewTrailerReqModel newTrailerReqModelFromJson(String str) => NewTrailerReqModel.fromJson(json.decode(str));

String newTrailerReqModelToJson(NewTrailerReqModel data) => json.encode(data.toJson());

class NewTrailerReqModel {
  NewTrailerReqModel({
    required this.carrier,
    required this.clientIds,
    required this.isHotTrailer,
    required this.owner,
    required this.remarks,
    required this.type,
    required this.unitNumber,
  });

  String carrier;
  List<String> clientIds;
  bool isHotTrailer;
  String owner;
  String remarks;
  String type;
  String unitNumber;

  factory NewTrailerReqModel.fromJson(Map<String, dynamic> json) => NewTrailerReqModel(
        carrier: json["carrier"],
        clientIds: List<String>.from(json["clientIds"].map((x) => x)),
        isHotTrailer: json["isHotTrailer"],
        owner: json["owner"],
        remarks: json["remarks"],
        type: json["type"],
        unitNumber: json["unitNumber"],
      );

  Map<String, dynamic> toJson() => {
        "carrier": carrier,
        "clientIds": clientIds == null ? null : List<dynamic>.from(clientIds.map((x) => x)),
        "isHotTrailer": isHotTrailer,
        "owner": owner,
        "remarks": remarks,
        "type": type,
        "unitNumber": unitNumber,
      };
}
