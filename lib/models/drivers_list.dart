// To parse this JSON data, do
//
//     final driversList = driversListFrom<PERSON>son(jsonString);

import 'dart:convert';

DriversList driversListFromJson(String str) => DriversList.fromJson(json.decode(str));

String driversListToJson(DriversList data) => json.encode(data.toJson());

class DriversList {
  DriversList({
    this.list = const [],
    this.page = 0,
    this.size = 0,
    this.totalElements = 0,
  });

  late final List<Driver> list;
  final int page;
  final int size;
  final int totalElements;

  factory DriversList.fromJson(Map<String, dynamic> json) => DriversList(
        list: json["list"] == null ? [] : List<Driver>.from(json["list"].map((x) => Driver.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": List<dynamic>.from(list.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class Driver {
  Driver({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
    this.isActive = false,
    this.roles = const [],
    this.clients = const [],
  });

  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? timeZone;
  final String? lastLoginTime;
  final bool isActive;
  final List<Role> roles;
  final List<Client> clients;

  factory Driver.fromJson(Map<String, dynamic> json) => Driver(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
        isActive: json["isActive"],
        roles: json["roles"] == null ? [] : List<Role>.from(json["roles"].map((x) => Role.fromJson(x))),
        clients: json["clients"] == null ? [] : List<Client>.from(json["clients"].map((x) => Client.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
        "isActive": isActive,
        "roles": roles == null ? null : List<dynamic>.from(roles.map((x) => x.toJson())),
        "clients": clients == null ? null : List<dynamic>.from(clients.map((x) => x.toJson())),
      };
}

class Client {
  Client({
    this.clientId,
    this.clientName,
    this.street,
    this.city,
    this.state,
    this.zip,
    this.country,
    this.contactPerson,
    this.contactEmail,
    this.contactPhone,
    this.remarks,
    this.isActive = false,
    this.audit,
  });

  final String? clientId;
  final String? clientName;
  final String? street;
  final String? city;
  final String? state;
  final String? zip;
  final String? country;
  final String? contactPerson;
  final String? contactEmail;
  final String? contactPhone;
  final String? remarks;
  final bool isActive;
  final Audit? audit;

  factory Client.fromJson(Map<String, dynamic> json) => Client(
        clientId: json["clientId"],
        clientName: json["clientName"],
        street: json["street"],
        city: json["city"],
        state: json["state"],
        zip: json["zip"],
        country: json["country"],
        contactPerson: json["contactPerson"],
        contactEmail: json["contactEmail"],
        contactPhone: json["contactPhone"],
        remarks: json["remarks"],
        isActive: json["isActive"],
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
      );

  Map<String, dynamic> toJson() => {
        "clientId": clientId,
        "clientName": clientName,
        "street": street,
        "city": city,
        "state": state,
        "zip": zip,
        "country": country,
        "contactPerson": contactPerson,
        "contactEmail": contactEmail,
        "contactPhone": contactPhone,
        "remarks": remarks,
        "isActive": isActive,
        "audit": audit == null ? null : audit?.toJson(),
      };
}

class Audit {
  Audit({
    this.createdDate,
    this.lastModifiedDate,
    this.createdBy,
    this.lastModifiedBy,
  });

  final String? createdDate;
  final String? lastModifiedDate;
  final EdBy? createdBy;
  final EdBy? lastModifiedBy;

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
        createdBy: json["createdBy"] == null ? null : EdBy.fromJson(json["createdBy"]),
        lastModifiedBy: json["lastModifiedBy"] == null ? null : EdBy.fromJson(json["lastModifiedBy"]),
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
        "createdBy": createdBy == null ? null : createdBy?.toJson(),
        "lastModifiedBy": lastModifiedBy == null ? null : lastModifiedBy?.toJson(),
      };
}

class EdBy {
  EdBy({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
    this.isActive = false,
    this.roles = const [],
  });

  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? timeZone;
  final String? lastLoginTime;
  final bool isActive;
  final List<Role> roles;

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
        isActive: json["isActive"],
        roles: json["roles"] == null ? [] : List<Role>.from(json["roles"].map((x) => Role.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
        "isActive": isActive,
        "roles": List<dynamic>.from(roles.map((x) => x.toJson())),
      };
}

class Role {
  Role({
    this.roleId,
    this.roleName,
    this.isActive = false,
  });

  final String? roleId;
  final String? roleName;
  final bool isActive;

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        roleId: json["roleId"],
        roleName: json["roleName"],
        isActive: json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "roleId": roleId,
        "roleName": roleName,
        "isActive": isActive,
      };
}
