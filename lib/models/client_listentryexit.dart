// To parse this JSON data, do
//
//     final clientListeEntryExit = clientListeEntryExitFromJson(jsonString);

import 'dart:convert';

ClientListeEntryExit clientListeEntryExitFromJson(String str) =>
    ClientListeEntryExit.fromJson(json.decode(str));

String clientListeEntryExitToJson(ClientListeEntryExit data) =>
    json.encode(data.toJson());

class ClientListeEntryExit {
  final List<ClientListElement>? list;
  final num? page;
  final num? size;
  final num? totalElements;

  ClientListeEntryExit({
    this.list,
    this.page,
    this.size,
    this.totalElements,
  });

  ClientListeEntryExit copyWith({
    List<ClientListElement>? list,
    num? page,
    num? size,
    num? totalElements,
  }) =>
      ClientListeEntryExit(
        list: list ?? this.list,
        page: page ?? this.page,
        size: size ?? this.size,
        totalElements: totalElements ?? this.totalElements,
      );

  factory ClientListeEntryExit.fromJson(Map<String, dynamic> json) =>
      ClientListeEntryExit(
        list: json["list"] == null
            ? []
            : List<ClientListElement>.from(
                json["list"]!.map((x) => ClientListElement.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": list == null
            ? []
            : List<dynamic>.from(list!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class ClientListElement {
  final String? clientId;
  final String? clientName;
  final String? street;
  final String? city;
  final String? state;
  final String? zip;
  final String? country;
  final String? contactPerson;
  final String? contactEmail;
  final String? contactPhone;
  final bool? isActive;
  final bool? dvir;
  final bool? bol;
  final bool? accountDeactivation;
  final bool? trailerAudit;
  final Audit? audit;
  final String? timeZone;
  final num? overTime;

  ClientListElement({
    this.clientId,
    this.clientName,
    this.street,
    this.city,
    this.state,
    this.zip,
    this.country,
    this.contactPerson,
    this.contactEmail,
    this.contactPhone,
    this.isActive,
    this.dvir,
    this.bol,
    this.accountDeactivation,
    this.trailerAudit,
    this.audit,
    this.timeZone,
    this.overTime,
  });

  ClientListElement copyWith({
    String? clientId,
    String? clientName,
    String? street,
    String? city,
    String? state,
    String? zip,
    String? country,
    String? contactPerson,
    String? contactEmail,
    String? contactPhone,
    bool? isActive,
    bool? dvir,
    bool? bol,
    bool? accountDeactivation,
    bool? trailerAudit,
    Audit? audit,
    String? timeZone,
    num? overTime,
  }) =>
      ClientListElement(
        clientId: clientId ?? this.clientId,
        clientName: clientName ?? this.clientName,
        street: street ?? this.street,
        city: city ?? this.city,
        state: state ?? this.state,
        zip: zip ?? this.zip,
        country: country ?? this.country,
        contactPerson: contactPerson ?? this.contactPerson,
        contactEmail: contactEmail ?? this.contactEmail,
        contactPhone: contactPhone ?? this.contactPhone,
        isActive: isActive ?? this.isActive,
        dvir: dvir ?? this.dvir,
        bol: bol ?? this.bol,
        accountDeactivation: accountDeactivation ?? this.accountDeactivation,
        trailerAudit: trailerAudit ?? this.trailerAudit,
        audit: audit ?? this.audit,
        timeZone: timeZone ?? this.timeZone,
        overTime: overTime ?? this.overTime,
      );

  factory ClientListElement.fromJson(Map<String, dynamic> json) =>
      ClientListElement(
        clientId: json["clientId"],
        clientName: json["clientName"],
        street: json["street"],
        city: json["city"],
        state: json["state"],
        zip: json["zip"],
        country: json["country"],
        contactPerson: json["contactPerson"],
        contactEmail: json["contactEmail"],
        contactPhone: json["contactPhone"],
        isActive: json["isActive"],
        dvir: json["dvir"],
        bol: json["bol"],
        accountDeactivation: json["accountDeactivation"],
        trailerAudit: json["trailerAudit"],
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
        timeZone: json["timeZone"],
        overTime: json["overTime"],
      );

  Map<String, dynamic> toJson() => {
        "clientId": clientId,
        "clientName": clientName,
        "street": street,
        "city": city,
        "state": state,
        "zip": zip,
        "country": country,
        "contactPerson": contactPerson,
        "contactEmail": contactEmail,
        "contactPhone": contactPhone,
        "isActive": isActive,
        "dvir": dvir,
        "bol": bol,
        "accountDeactivation": accountDeactivation,
        "trailerAudit": trailerAudit,
        "audit": audit?.toJson(),
        "timeZone": timeZone,
        "overTime": overTime,
      };
}

class Audit {
  final String? createdDate;
  final String? lastModifiedDate;
  final EdBy? createdBy;
  final EdBy? lastModifiedBy;

  Audit({
    this.createdDate,
    this.lastModifiedDate,
    this.createdBy,
    this.lastModifiedBy,
  });

  Audit copyWith({
    String? createdDate,
    String? lastModifiedDate,
    EdBy? createdBy,
    EdBy? lastModifiedBy,
  }) =>
      Audit(
        createdDate: createdDate ?? this.createdDate,
        lastModifiedDate: lastModifiedDate ?? this.lastModifiedDate,
        createdBy: createdBy ?? this.createdBy,
        lastModifiedBy: lastModifiedBy ?? this.lastModifiedBy,
      );

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
        createdBy:
            json["createdBy"] == null ? null : EdBy.fromJson(json["createdBy"]),
        lastModifiedBy: json["lastModifiedBy"] == null
            ? null
            : EdBy.fromJson(json["lastModifiedBy"]),
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
        "createdBy": createdBy?.toJson(),
        "lastModifiedBy": lastModifiedBy?.toJson(),
      };
}

class EdBy {
  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? timeZone;
  final String? lastLoginTime;
  final bool? isActive;
  final bool? isAdUser;

  EdBy({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
    this.isActive,
    this.isAdUser,
  });

  EdBy copyWith({
    String? userId,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? timeZone,
    String? lastLoginTime,
    bool? isActive,
    bool? isAdUser,
  }) =>
      EdBy(
        userId: userId ?? this.userId,
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        timeZone: timeZone ?? this.timeZone,
        lastLoginTime: lastLoginTime ?? this.lastLoginTime,
        isActive: isActive ?? this.isActive,
        isAdUser: isAdUser ?? this.isAdUser,
      );

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
        isActive: json["isActive"],
        isAdUser: json["isADUser"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
        "isActive": isActive,
        "isADUser": isAdUser,
      };
}
