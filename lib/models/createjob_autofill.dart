import 'dart:convert';

CreateJobTrailerAutofillModel createJobTrailerAutofillModelFromJson(String str) => CreateJobTrailerAutofillModel.fromJson(json.decode(str));

String createJobTrailerAutofillModelToJson(CreateJobTrailerAutofillModel data) => json.encode(data.toJson());

class CreateJobTrailerAutofillModel {
  final String? fleetId;
  final String? type;
  final String? unitNumber;
  final String? remarks;
  final bool? isActive;
  final String? fleetStatus;
  final Spot? spot;
  final List<String>? clientIds;
  final Audit? audit;

  CreateJobTrailerAutofillModel({
    this.fleetId,
    this.type,
    this.unitNumber,
    this.remarks,
    this.isActive,
    this.fleetStatus,
    this.spot,
    this.clientIds,
    this.audit,
  });

  CreateJobTrailerAutofillModel copyWith({
    String? fleetId,
    String? type,
    String? unitNumber,
    String? remarks,
    bool? isActive,
    String? fleetStatus,
    Spot? spot,
    List<String>? clientIds,
    Audit? audit,
  }) => CreateJobTrailerAutofillModel(
        fleetId: fleetId ?? this.fleetId,
        type: type ?? this.type,
        unitNumber: unitNumber ?? this.unitNumber,
        remarks: remarks ?? this.remarks,
        isActive: isActive ?? this.isActive,
        fleetStatus: fleetStatus ?? this.fleetStatus,
        spot: spot ?? this.spot,
        clientIds: clientIds ?? this.clientIds,
        audit: audit ?? this.audit,
      );

  factory CreateJobTrailerAutofillModel.fromJson(Map<String, dynamic> json) =>
      CreateJobTrailerAutofillModel(
        fleetId: json["fleetId"],
        type: json["type"],
        unitNumber: json["unitNumber"],
        remarks: json["remarks"],
        isActive: json["isActive"],
        fleetStatus: json["fleetStatus"],
        spot: json["spot"] == null ? null : Spot.fromJson(json["spot"]),
        clientIds: json["clientIds"] == null ? [] : List<String>.from(json["clientIds"]!.map((x) => x)),
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
      );

  Map<String, dynamic> toJson() => {
        "fleetId": fleetId,
        "type": type,
        "unitNumber": unitNumber,
        "remarks": remarks,
        "isActive": isActive,
        "fleetStatus": fleetStatus,
        "spot": spot?.toJson(),
        "clientIds": clientIds == null ? [] : List<dynamic>.from(clientIds!.map((x) => x)),
        "audit": audit?.toJson(),
      };
}

class Audit {
  final String? createdDate;
  final String? lastModifiedDate;
  final EdBy? createdBy;
  final EdBy? lastModifiedBy;

  Audit({
    this.createdDate,
    this.lastModifiedDate,
    this.createdBy,
    this.lastModifiedBy,
  });

  Audit copyWith({
    String? createdDate,
    String? lastModifiedDate,
    EdBy? createdBy,
    EdBy? lastModifiedBy,
  }) =>
      Audit(
        createdDate: createdDate ?? this.createdDate,
        lastModifiedDate: lastModifiedDate ?? this.lastModifiedDate,
        createdBy: createdBy ?? this.createdBy,
        lastModifiedBy: lastModifiedBy ?? this.lastModifiedBy,
      );

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
        createdBy: json["createdBy"] == null ? null : EdBy.fromJson(json["createdBy"]),
        lastModifiedBy: json["lastModifiedBy"] == null ? null : EdBy.fromJson(json["lastModifiedBy"]),
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
        "createdBy": createdBy?.toJson(),
        "lastModifiedBy": lastModifiedBy?.toJson(),
      };
}

class EdBy {
  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? timeZone;
  final String? lastLoginTime;
  final bool? isActive;
  final bool? isAdUser;

  EdBy({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
    this.isActive,
    this.isAdUser,
  });

  EdBy copyWith({
    String? userId,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? timeZone,
    String? lastLoginTime,
    bool? isActive,
    bool? isAdUser,
  }) =>
      EdBy(
        userId: userId ?? this.userId,
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        timeZone: timeZone ?? this.timeZone,
        lastLoginTime: lastLoginTime ?? this.lastLoginTime,
        isActive: isActive ?? this.isActive,
        isAdUser: isAdUser ?? this.isAdUser,
      );

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
        isActive: json["isActive"],
        isAdUser: json["isADUser"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
        "isActive": isActive,
        "isADUser": isAdUser,
      };
}

class Spot {
  final String? locationId;
  final String? spotId;
  final String? spotName;
  final String? type;
  final String? status;
  final num? latitude; // Changed from int? to num?
  final num? longitude; // Changed from int? to num?
  final String? remarks;
  final bool? isActive;
  final num? emptiedSinceSeconds; // Changed from dynamic to num?
  final num? occupiedSinceSeconds; // Changed from dynamic to num?
  final Fleet? fleet;
  final String? locationName;
  final DateTime? lastOccupiedTime;
  final dynamic? lastEmptiedTime; // Changed from dynamic to num?
  final bool? isOccupied;
  final dynamic audit;

  Spot({
    this.locationId,
    this.spotId,
    this.spotName,
    this.type,
    this.status,
    this.latitude,
    this.longitude,
    this.remarks,
    this.isActive,
    this.emptiedSinceSeconds,
    this.occupiedSinceSeconds,
    this.fleet,
    this.locationName,
    this.lastOccupiedTime,
    this.lastEmptiedTime,
    this.isOccupied,
    this.audit,
  });

  Spot copyWith({
    String? locationId,
    String? spotId,
    String? spotName,
    String? type,
    String? status,
    num? latitude, // Changed to num
    num? longitude, // Changed to num
    String? remarks,
    bool? isActive,
    num? emptiedSinceSeconds, // Changed to num
    num? occupiedSinceSeconds, // Changed to num
    Fleet? fleet,
    String? locationName,
    DateTime? lastOccupiedTime,
    String? lastEmptiedTime, // Changed to num
    bool? isOccupied,
    dynamic audit,
  }) =>
      Spot(
        locationId: locationId ?? this.locationId,
        spotId: spotId ?? this.spotId,
        spotName: spotName ?? this.spotName,
        type: type ?? this.type,
        status: status ?? this.status,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        remarks: remarks ?? this.remarks,
        isActive: isActive ?? this.isActive,
        emptiedSinceSeconds: emptiedSinceSeconds ?? this.emptiedSinceSeconds,
        occupiedSinceSeconds: occupiedSinceSeconds ?? this.occupiedSinceSeconds,
        fleet: fleet ?? this.fleet,
        locationName: locationName ?? this.locationName,
        lastOccupiedTime: lastOccupiedTime ?? this.lastOccupiedTime,
        lastEmptiedTime: lastEmptiedTime ?? this.lastEmptiedTime,
        isOccupied: isOccupied ?? this.isOccupied,
        audit: audit ?? this.audit,
      );

  factory Spot.fromJson(Map<String, dynamic> json) => Spot(
        locationId: json["locationId"],
        spotId: json["spotId"],
        spotName: json["spotName"],
        type: json["type"],
        status: json["status"],
        latitude: json["latitude"], // Changed to num
        longitude: json["longitude"], // Changed to num
        remarks: json["remarks"],
        isActive: json["isActive"],
        emptiedSinceSeconds: json["emptiedSinceSeconds"], // Changed to num
        occupiedSinceSeconds: json["occupiedSinceSeconds"], // Changed to num
        fleet: json["fleet"] == null ? null : Fleet.fromJson(json["fleet"]),
        locationName: json["locationName"],
        lastOccupiedTime: json["lastOccupiedTime"] == null ? null : DateTime.parse(json["lastOccupiedTime"]),
        lastEmptiedTime: json["lastEmptiedTime"], // Changed to num
        isOccupied: json["isOccupied"],
        audit: json["audit"],
      );

  Map<String, dynamic> toJson() => {
        "locationId": locationId,
        "spotId": spotId,
        "spotName": spotName,
        "type": type,
        "status": status,
        "latitude": latitude,
        "longitude": longitude,
        "remarks": remarks,
        "isActive": isActive,
        "emptiedSinceSeconds": emptiedSinceSeconds,
        "occupiedSinceSeconds": occupiedSinceSeconds,
        "fleet": fleet?.toJson(),
        "locationName": locationName,
        "lastOccupiedTime": lastOccupiedTime?.toIso8601String(),
        "lastEmptiedTime": lastEmptiedTime,
        "isOccupied": isOccupied,
        "audit": audit,
      };
}

class Fleet {
  final String? fleetId;
  final String? type;
  final String? unitNumber;
  final String? remarks;
  final bool? isActive;
  final String? fleetStatus;

  Fleet({
    this.fleetId,
    this.type,
    this.unitNumber,
    this.remarks,
    this.isActive,
    this.fleetStatus,
  });

  Fleet copyWith({
    String? fleetId,
    String? type,
    String? unitNumber,
    String? remarks,
    bool? isActive,
    String? fleetStatus,
  }) =>
      Fleet(
        fleetId: fleetId ?? this.fleetId,
        type: type ?? this.type,
        unitNumber: unitNumber ?? this.unitNumber,
        remarks: remarks ?? this.remarks,
        isActive: isActive ?? this.isActive,
        fleetStatus: fleetStatus ?? this.fleetStatus,
      );

  factory Fleet.fromJson(Map<String, dynamic> json) => Fleet(
        fleetId: json["fleetId"],
        type: json["type"],
        unitNumber: json["unitNumber"],
        remarks: json["remarks"],
        isActive: json["isActive"],
        fleetStatus: json["fleetStatus"],
      );

  Map<String, dynamic> toJson() => {
        "fleetId": fleetId,
        "type": type,
        "unitNumber": unitNumber,
        "remarks": remarks,
        "isActive": isActive,
        "fleetStatus": fleetStatus,
      };
}
