import 'dart:convert';

LocationListeEntryExit locationListeEntryExitFromJson(String str) =>
    LocationListeEntryExit.fromJson(json.decode(str));

String locationListeEntryExitToJson(LocationListeEntryExit data) =>
    json.encode(data.toJson());

class LocationListeEntryExit {
  final List<ListLocationElement>? list;
  final int? page;
  final int? size;
  final int? totalElements;

  LocationListeEntryExit({
    this.list,
    this.page,
    this.size,
    this.totalElements,
  });

  LocationListeEntryExit copyWith({
    List<ListLocationElement>? list,
    int? page,
    int? size,
    int? totalElements,
  }) =>
      LocationListeEntryExit(
        list: list ?? this.list,
        page: page ?? this.page,
        size: size ?? this.size,
        totalElements: totalElements ?? this.totalElements,
      );

  factory LocationListeEntryExit.fromJson(Map<String, dynamic> json) =>
      LocationListeEntryExit(
        list: json["list"] == null
            ? []
            : List<ListLocationElement>.from(
                json["list"]!.map((x) => ListLocationElement.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": list == null
            ? []
            : List<dynamic>.from(list!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class ListLocationElement {
  final String? clientId;
  final String? locationId;
  final String? locationName;
  final String? street;
  final String? city;
  final String? state;
  final String? zip;
  final String? country;
  final num? latitude; // Changed from int to num
  final num? longitude; // Changed from int to num
  final String? locationMapJson;
  final String? remarks;
  final bool? isActive;
  final bool? isDefault;
  final String? mapImageUrl;
  final String? pieChartColor;
  final Audit? audit;

  ListLocationElement({
    this.clientId,
    this.locationId,
    this.locationName,
    this.street,
    this.city,
    this.state,
    this.zip,
    this.country,
    this.latitude,
    this.longitude,
    this.locationMapJson,
    this.remarks,
    this.isActive,
    this.isDefault,
    this.mapImageUrl,
    this.pieChartColor,
    this.audit,
  });

  ListLocationElement copyWith({
    String? clientId,
    String? locationId,
    String? locationName,
    String? street,
    String? city,
    String? state,
    String? zip,
    String? country,
    num? latitude, // Updated to num
    num? longitude, // Updated to num
    String? locationMapJson,
    String? remarks,
    bool? isActive,
    bool? isDefault,
    String? mapImageUrl,
    String? pieChartColor,
    Audit? audit,
  }) =>
      ListLocationElement(
        clientId: clientId ?? this.clientId,
        locationId: locationId ?? this.locationId,
        locationName: locationName ?? this.locationName,
        street: street ?? this.street,
        city: city ?? this.city,
        state: state ?? this.state,
        zip: zip ?? this.zip,
        country: country ?? this.country,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        locationMapJson: locationMapJson ?? this.locationMapJson,
        remarks: remarks ?? this.remarks,
        isActive: isActive ?? this.isActive,
        isDefault: isDefault ?? this.isDefault,
        mapImageUrl: mapImageUrl ?? this.mapImageUrl,
        pieChartColor: pieChartColor ?? this.pieChartColor,
        audit: audit ?? this.audit,
      );

  factory ListLocationElement.fromJson(Map<String, dynamic> json) =>
      ListLocationElement(
        clientId: json["clientId"],
        locationId: json["locationId"],
        locationName: json["locationName"],
        street: json["street"],
        city: json["city"],
        state: json["state"],
        zip: json["zip"],
        country: json["country"],
        latitude: json["latitude"] == null
            ? null
            : json["latitude"] as num, // Updated to num
        longitude: json["longitude"] == null
            ? null
            : json["longitude"] as num, // Updated to num
        locationMapJson: json["locationMapJson"],
        remarks: json["remarks"],
        isActive: json["isActive"],
        isDefault: json["isDefault"],
        mapImageUrl: json["mapImageUrl"],
        pieChartColor: json["pieChartColor"],
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
      );

  Map<String, dynamic> toJson() => {
        "clientId": clientId,
        "locationId": locationId,
        "locationName": locationName,
        "street": street,
        "city": city,
        "state": state,
        "zip": zip,
        "country": country,
        "latitude": latitude,
        "longitude": longitude,
        "locationMapJson": locationMapJson,
        "remarks": remarks,
        "isActive": isActive,
        "isDefault": isDefault,
        "mapImageUrl": mapImageUrl,
        "pieChartColor": pieChartColor,
        "audit": audit?.toJson(),
      };
}

class Audit {
  final String? createdDate;
  final String? lastModifiedDate;
  final EdBy? createdBy;
  final EdBy? lastModifiedBy;

  Audit({
    this.createdDate,
    this.lastModifiedDate,
    this.createdBy,
    this.lastModifiedBy,
  });

  Audit copyWith({
    String? createdDate,
    String? lastModifiedDate,
    EdBy? createdBy,
    EdBy? lastModifiedBy,
  }) =>
      Audit(
        createdDate: createdDate ?? this.createdDate,
        lastModifiedDate: lastModifiedDate ?? this.lastModifiedDate,
        createdBy: createdBy ?? this.createdBy,
        lastModifiedBy: lastModifiedBy ?? this.lastModifiedBy,
      );

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
        createdBy:
            json["createdBy"] == null ? null : EdBy.fromJson(json["createdBy"]),
        lastModifiedBy: json["lastModifiedBy"] == null
            ? null
            : EdBy.fromJson(json["lastModifiedBy"]),
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
        "createdBy": createdBy?.toJson(),
        "lastModifiedBy": lastModifiedBy?.toJson(),
      };
}

class EdBy {
  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? timeZone;
  final String? lastLoginTime;
  final bool? isActive;
  final bool? isAdUser;

  EdBy({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
    this.isActive,
    this.isAdUser,
  });

  EdBy copyWith({
    String? userId,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? timeZone,
    String? lastLoginTime,
    bool? isActive,
    bool? isAdUser,
  }) =>
      EdBy(
        userId: userId ?? this.userId,
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        timeZone: timeZone ?? this.timeZone,
        lastLoginTime: lastLoginTime ?? this.lastLoginTime,
        isActive: isActive ?? this.isActive,
        isAdUser: isAdUser ?? this.isAdUser,
      );

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
        isActive: json["isActive"],
        isAdUser: json["isADUser"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
        "isActive": isActive,
        "isADUser": isAdUser,
      };
}
