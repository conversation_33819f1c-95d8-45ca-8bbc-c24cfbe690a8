import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:spot_on/cubits/create_job/create_job_cubit.dart';
import 'package:spot_on/models/autopopulateexit.dart';
import 'package:spot_on/models/drivers_list.dart';
import 'package:spot_on/models/login/login_response_model.dart';
import 'package:spot_on/models/spot_list_entryexit.dart';
import 'package:spot_on/models/spot_on_locations.dart' as spot;
import 'package:spot_on/models/truck_list_model.dart';
import 'package:spot_on/providers/create_job_state.dart';

import 'package:spot_on/utils/gaps.dart';
import 'package:spot_on/utils/imports.dart';
import 'package:spot_on/utils/utils.dart';
import 'package:spot_on/widgets/add_trailer.dart';
import 'package:spot_on/widgets/app_ta.dart';
import 'package:spot_on/widgets/job_priorities.dart';

import '../models/spots_list.dart' as sp;
import '../models/spots_list.dart';

class CreateJobScreen extends StatefulWidget {
  const CreateJobScreen({Key? key}) : super(key: key);

  @override
  State<CreateJobScreen> createState() => _CreateJobScreenState();
}

class _CreateJobScreenState extends State<CreateJobScreen> {
  Driver? newdr;
  spot.SpotOnLocation? spotOnLocationSelected;
  String selectedSPOT = '';

  @override
  Widget build(BuildContext context) {
    // Create new Driver items
    Driver clientSpotter = Driver(
        firstName: 'BUCKET SYSTEM SPOTTER',
        lastName: "",
        roles: [Role(roleName: 'CLIENT')]);
    Driver newDriver1 = Driver(
        firstName: 'BUCKET SYSTEM DRIVER',
        lastName: "",
        roles: [Role(roleName: 'DRIVER')]);
    Driver newDriver2 = Driver(
        firstName: 'BUCKET SYSTEM SPOTTER',
        lastName: "",
        roles: [Role(roleName: 'SPOTTER')]);

// Create the dropdown items with new drivers first
    List<Driver> dropdownItems = [newDriver1, newDriver2];
    CreateJobState createJobState = context.watch<CreateJobState>();
    print('riiiiii>>>>77777>>>${createJobState.driversList.list.length}');

    print('object>>>>>dr>>>>>>${createJobState.driversList.list}');
    print('object>>>>>>mmmmmm>>>>>${createJobState.selectedDriver}');
    // dropdownItems.addAll(createJobState.driversList.list
    //     .map<DropdownMenuItem<Driver>>((Driver driver) {
    //   return DropdownMenuItem<Driver>(
    //     value: driver,
    //     child: AppTxt(
    //       text: '${driver.firstName} ${driver.lastName}',
    //       fontWeight: FontWeight.bold,
    //     ),
    //   );
    // }).toList());
    bool driverExists(List<Driver> drivers, Driver driver) {
      return drivers.any((d) =>
          d.firstName == driver.firstName && d.lastName == driver.lastName);
    }

    driverCheck() {
      setState(() {
        //commented---bucket
        //----commented

        createJobState.jobRequestModel.bucket = 'NIL';
      });
    }
    //commented---bucket-

    (isDriver() || isSpotter() || isGuard())
        ? []
        : !driverExists(createJobState.driversList.list, newDriver1)
            ? createJobState.driversList.list.addAll(dropdownItems)
            : [];
    // commented---bucket-

    // List<Driver> sortedDriverList = [
    //   // Add special drivers first
    //   if (createJobState.driversList.list
    //       .any((d) => d.firstName == newDriver1.firstName))
    //     newDriver1,
    //   if (createJobState.driversList.list
    //       .any((d) => d.firstName == newDriver2.firstName))
    //     newDriver2,
    //   // Add the remaining drivers
    //   ...createJobState.driversList.list.where((d) =>
    //       !(d.firstName == newDriver1.firstName) &&
    //       !(d.firstName == newDriver2.firstName)),
    // ];
    // print(
    // '-------88-------------${sortedDriverList}--------${sortedDriverList.length}');
    // for (var i in sortedDriverList) {
    //   print('name--0--${i.firstName}');
    // }

    //commented----
    // (isDriver() || isSpotter() || isGuard())
    //                       ?[]:createJobState.driversList.list.add(newDriver1);
    //                       (isDriver() || isSpotter() || isGuard())
    //                       ?[]:createJobState.driversList.list.add(newDriver2);
    print('riiiiii>>>>>>>${createJobState.driversList.list.length}');
    for (var i in (createJobState.driversList.list)) {
      print('repeat---${i?.firstName}---');
      print('repeat---${i}---');

      //   print('repeat---${createJobState.selectedDriver?.firstName}---');

      // if(createJobState.selectedDriver?.firstName==i.firstName){
      //   print('repeat');
      // }
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const AppTxt(text: ''),
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: appGrey),
          onPressed: () async {
            closeScreen();
            hideSnackBar();
            createJobState.clearSelections();
          },
        ),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      body: BlocConsumer<CreateJobTrailerCubit, CreateJobTrailerState>(
        listener: (context, state) {
          print('jino----44$state');

          if (state is CreateJobInitial) {
            const Center(
              child: CircularProgressIndicator(),
            );
          }

          // TODO: implement listener
          if (state is SuccessAutofill) {
            print('jjjjjj-----${state.list.spot?.locationName}');
            print('success fill');
            if (state.list.spot?.locationName != null) {
              setState(() {
                createJobState.selectedPickUpLocation = spot.SpotOnLocation(
                  locationName: state.list.spot?.locationName,
                  clientId: '6757656576',
                  locationId: state.list.spot?.locationId,
                  street: '123 Main St',
                  city: 'Anytown',
                  state: 'CA',
                  zip: '12345',
                  country: 'USA',
                  latitude: 34.0522,
                  longitude: -118.2437,
                  remarks: 'Headquarters',
                  isActive: true,
                  audit: null,
                  trailerStandPhoto: false,
                );
              });
              selectedSPOT = state.list.spot!.spotId.toString();
              context.read<CreateJobTrailerCubit>().getSpotCreateJob(
                  '${state.list.spot?.locationId.toString()}');
            }
          }
          if (state is SuccessSpot) {
            print(
                'ghghghghghghghg${state.createJobSpotList?.list?.first.spotName}');

            List<sp.Spot> spotsData = [];
            for (var i in state.createJobSpotList?.list ?? []) {
              print('mmmmmmmm${i.spotId}');
              spotsData.add(sp.Spot(
                locationId: i.locationId,
                spotId: i.spotId,
                spotName: i.spotName,
                type: 'Parking',
                status: 'Available',
                latitude: 34.0522,
                longitude: -118.2437,
                remarks: 'Near the entrance',
                isActive: true,
                fleet: null,
                audit: null,
              ));
            }

            createJobState.pickupSpotsList = SpotsList(
              list: spotsData,
              page: 0,
              size: spotsData.length,
              totalElements: spotsData.length,
            );
            print('dmmmmmmmmmmmmmmm${createJobState.pickupSpotsList.list}');
          }
        },
        builder: (context, state) {
          return ModalProgressHUD(
            inAsyncCall: createJobState.truckListLoading ||
                createJobState.spotsLoading ||
                createJobState.spotOnLocationsLoading,
            opacity: 0.1,
            color: Colors.transparent,
            blur: 0.3,
            child: GlobalWidget(
              child: Container(
                color: Colors.white,
                padding: const EdgeInsets.all(20),
                child: ListView(
                  children: [
                    const AppTxt(
                      text: 'Create New Spot',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    const SizedBox(height: 30),
                    const AppTxt(
                      text: 'Trailer',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    TypeAheadField(
                      textFieldConfiguration: TextFieldConfiguration(
                        autofocus: false,
                        decoration: const InputDecoration(
                          hintText: "Search Trailer",
                        ),
                        controller: createJobState.autoCompleteController,
                      ),
                      getImmediateSuggestions: false,
                      hideOnEmpty: true,
                      minCharsForSuggestions: 1,
                      suggestionsCallback: (pattern) async {
                        var result = await Services.getTruckListNew(pattern);
                        if (result is Success) {
                          var truckListModel =
                              result.response as TruckListModel;
                          return truckListModel.list.where(
                            (e) => e.unitNumber!.toLowerCase().contains(
                                  pattern.toLowerCase(),
                                ),
                          );
                        }
                        return [];
                      },
                      loadingBuilder: (context) {
                        return const Center(
                          child: SizedBox(
                            height: 16,
                            width: 16,
                            child: CircularProgressIndicator(strokeWidth: 1),
                          ),
                        );
                      },
                      itemBuilder: (context, suggestion) {
                        TruckDetail? truckDetail = suggestion as TruckDetail;
                        return Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: AppTxt(
                            text:
                                truckDetail.unitNumber?.replaceAll('-', ' ') ??
                                    '',
                            color: truckDetail.isHotTrailer
                                ? Colors.red
                                : Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      },
                      onSuggestionSelected: (suggestion) {
                        TruckDetail? truckDetail = suggestion as TruckDetail;
                        if (truckDetail == null) {
                          return;
                        }
                        createJobState.selectedPickUpLocation = null;
                        createJobState.jobRequestModel.pickupLocationId = '';
                        createJobState.selectedPickupSpot = null;
                        createJobState.jobRequestModel.pickupSpotId = '';
                        selectedSPOT = '';

                        createJobState.selectedTruckDetail = truckDetail;
                        createJobState.jobRequestModel.fleetId =
                            truckDetail.fleetId!;
                        createJobState.autoCompleteController.text =
                            truckDetail.unitNumber ?? '';
                        createJobState.refresh();
                        context
                            .read<CreateJobTrailerCubit>()
                            .autofillSpot('${suggestion.fleetId}');
                      },
                    ),
                    const SizedBox(height: 20),
                    const AddTrailer(),
                    const SizedBox(height: 20),
                    const AppTxt(
                      text: 'Pickup Location',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    DropdownButton<spot.SpotOnLocation>(
                      hint: const AppTxt(text: 'Select Pick Up Location'),
                      value: getLocationsValue(
                          createJobState.selectedPickUpLocation,
                          createJobState),
                      icon: createJobState.spotsLoading
                          ? const CupertinoActivityIndicator()
                          : const Icon(Icons.arrow_drop_down_outlined),
                      iconSize: 24,
                      elevation: 16,
                      isExpanded: true,
                      style: TextStyle(color: Theme.of(context).primaryColor),
                      onChanged: (spot.SpotOnLocation? spotOnLocation) {
                        if (createJobState.spotOnLocationsLoading) {
                          return;
                        }
                        selectedSPOT = '';
                        createJobState.jobRequestModel.pickupSpotId = '';
                        createJobState.selectedPickupSpot = null;
                        createJobState.selectedPickUpLocation = spotOnLocation;
                        createJobState.jobRequestModel.pickupLocationId =
                            spotOnLocation!.locationId!;
                        print(
                            'created----pickspot----${spotOnLocation.locationId}');
                          !(isSpotter() || isDriver())?  createJobState.getDriversList(
                          createJobState.jobRequestModel.pickupLocationId ?? "",
                          createJobState.jobRequestModel.dropLocationId ?? ""
                        ):SizedBox();

                        createJobState.getSpots(
                            locationId: spotOnLocation.locationId!,
                            drop: false);
                        createJobState.refresh();
                      },
                      items: createJobState.spotOnLocationList.list
                          .map<DropdownMenuItem<spot.SpotOnLocation>>(
                              (spot.SpotOnLocation value) {
                        return DropdownMenuItem<spot.SpotOnLocation>(
                          value: value,
                          child: AppTxt(
                            text: value.locationName ?? '',
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 20),
                    const AppTxt(
                      text: 'Pickup Spot',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    DropdownButton<sp.Spot>(
                      hint: const AppTxt(text: 'Select Pick Up Spot'),
                      value: selectedSPOT != ''
                          ? getSpotValue(
                              createJobState.selectedPickupSpot, createJobState)
                          : createJobState.selectedPickupSpot,
                      icon: createJobState.spotsLoading
                          ? const CupertinoActivityIndicator()
                          : const Icon(Icons.arrow_drop_down_outlined),
                      iconSize: 24,
                      elevation: 16,
                      isExpanded: true,
                      style: const TextStyle(color: appBg),
                      onChanged: (sp.Spot? spot) {
                        if (createJobState.spotsLoading) {
                          return;
                        }
                        selectedSPOT = '';
                        createJobState.selectedPickupSpot = spot;
                        createJobState.jobRequestModel.pickupSpotId =
                            spot!.spotId!;
                        createJobState.refresh();
                      },
                      items: createJobState.pickupSpotsList.list
                          .map<DropdownMenuItem<sp.Spot>>((sp.Spot spot) {
                        String status = getSpotIdStatus(spot);
                        return DropdownMenuItem<sp.Spot>(
                          value: spot,
                          child: Column(
                            children: [
                              const SizedBox(height: 20),
                              Row(
                                children: [
                                  const SizedBox(width: 5),
                                  AppTxt(
                                    text: '${spot.spotName}',
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const SizedBox(width: 10),
                                  const Spacer(),
                                  AppTxt(
                                    text: status,
                                    color: appBg,
                                    fontSize: 14,
                                  ),
                                  const SizedBox(width: 10),
                                ],
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 20),
                    const AppTxt(
                      text: 'Drop Location',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    DropdownButton<spot.SpotOnLocation>(
                      hint: const AppTxt(text: 'Select Drop Location'),
                      value: createJobState.selectedDropLocation,
                      icon: createJobState.spotsLoading
                          ? const CupertinoActivityIndicator()
                          : const Icon(Icons.arrow_drop_down_outlined),
                      iconSize: 24,
                      elevation: 16,
                      isExpanded: true,
                      style: TextStyle(color: Theme.of(context).primaryColor),
                      onChanged: (spot.SpotOnLocation? spotOnLocation) {
                        if (createJobState.spotOnLocationsLoading) {
                          return;
                        }
                        createJobState.selectedDropSpot = null;
                        createJobState.selectedDropLocation = spotOnLocation;
                        createJobState.jobRequestModel.dropLocationId =
                            spotOnLocation!.locationId!;
                        print(
                            'created----dropspot----${spotOnLocation.locationId}');
                       !(isSpotter() || isDriver() )?  createJobState.getDriversList(
                          createJobState.jobRequestModel.pickupLocationId ?? "",
                          createJobState.jobRequestModel.dropLocationId ?? ''
                        ):SizedBox();

                        createJobState.getSpots(
                            locationId: spotOnLocation.locationId!, drop: true);
                        createJobState.refresh();
                      },
                      items: createJobState.spotOnLocationList.list
                          .map<DropdownMenuItem<spot.SpotOnLocation>>(
                              (spot.SpotOnLocation value) {
                        return DropdownMenuItem<spot.SpotOnLocation>(
                          value: value,
                          child: AppTxt(
                            text: value.locationName ?? '',
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 20),
                    const AppTxt(
                      text: 'Drop Spot',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    DropdownButton<sp.Spot>(
                      hint: const AppTxt(text: 'Select Drop Spot'),
                      value: createJobState.selectedDropSpot,
                      icon: createJobState.spotsLoading
                          ? const CupertinoActivityIndicator()
                          : const Icon(Icons.arrow_drop_down_outlined),
                      iconSize: 24,
                      elevation: 16,
                      isExpanded: true,
                      style: TextStyle(color: Theme.of(context).primaryColor),
                      onChanged: (sp.Spot? spot) {
                        if (createJobState.spotsLoading) {
                          return;
                        }
                        createJobState.selectedDropSpot = spot;
                        createJobState.jobRequestModel.dropSpotId =
                            spot!.spotId!;
                        createJobState.refresh();
                      },
                      items: createJobState.dropSpotsList.list
                          .map<DropdownMenuItem<sp.Spot>>((sp.Spot spot) {
                        String status = getSpotIdStatus(spot);
                        return DropdownMenuItem<sp.Spot>(
                          value: spot,
                          child: Column(
                            children: [
                              const SizedBox(height: 20),
                              Row(
                                children: [
                                  const SizedBox(width: 5),
                                  AppTxt(
                                    text: '${spot.spotName}',
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const SizedBox(width: 10),
                                  const Spacer(),
                                  AppTxt(
                                    text: status,
                                    color: appBg,
                                    fontSize: 14,
                                  ),
                                  const SizedBox(width: 10),
                                ],
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                    gapH6,
                    AppTA(
                      hintText: 'Notes',
                      labelText: 'Notes',
                      minLines: 1,
                      onTextChange: (val) async {
                        createJobState.jobRequestModel.description = val;
                      },
                    ),
                    gapH32,
                    const AppTxt(
                      text: 'Trailer Status',
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    DropdownButton<FleetStatus>(
                      hint: const AppTxt(text: 'Select Trailer Status'),
                      value: createJobState.selectedFleetStatus,
                      icon: createJobState.truckListLoading
                          ? const CupertinoActivityIndicator()
                          : const Icon(Icons.arrow_drop_down_outlined),
                      iconSize: 24,
                      elevation: 16,
                      isExpanded: true,
                      style: TextStyle(color: Theme.of(context).primaryColor),
                      onChanged: (FleetStatus? fleetStatus) {
                        createJobState.selectedFleetStatus = fleetStatus;
                        createJobState.jobRequestModel.fleetStatus =
                            fleetStatus!.id;
                        createJobState.refresh();
                      },
                      items: createJobState.fleetStatuses
                          .map<DropdownMenuItem<FleetStatus>>(
                              (FleetStatus value) {
                        return DropdownMenuItem<FleetStatus>(
                          value: value,
                          child: AppTxt(
                            text: value.name == 'Full' ? 'Loaded' : value.name,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 20),
                    Visibility(
                      visible: !(isDriver() || isSpotter()),
                      child:  AppTxt(
                        text:isClient()? 'Bucket spotters' : 'Driver/Spotter',
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Visibility(
                      visible: !(isDriver() || isSpotter()),
                      child: DropdownButton<Driver>(
                          value:isClient()?  clientSpotter: createJobState.selectedDriver,
                          hint:  AppTxt(text:isClient()? 'Select Bucket spotters' :  'Select Driver'),
                          icon: createJobState.truckListLoading
                              ? const CupertinoActivityIndicator()
                              : const Icon(Icons.arrow_drop_down_outlined),
                          iconSize: 24,
                          elevation: 16,
                          isExpanded: true,
                          style:
                              TextStyle(color: Theme.of(context).primaryColor),
                          onChanged: (Driver? driver) {
                            if (createJobState.truckListLoading) {
                              return;
                            }
                            //commented----bucket-----
                            if (driver?.firstName == 'BUCKET SYSTEM SPOTTER' ||
                                driver?.firstName == 'BUCKET SYSTEM DRIVER') {
                              var val;
                              if (driver?.firstName ==
                                  'BUCKET SYSTEM SPOTTER') {
                                setState(() {
                                  val = 'BUCKET_SPOTTER';
                                });
                              } else {
                                setState(() {
                                  val = 'BUCKET_DRIVER';
                                });
                              }
                              createJobState.selectedDriver = driver;
                              createJobState.jobRequestModel.assignedToUserId =
                                  '';
                              createJobState.jobRequestModel.bucket = val ?? '';
                              print('...............${newdr!.firstName}');
                              createJobState.refresh();
                            } else {
                              //commented-----bucket------

                              createJobState.selectedDriver = driver;

                              createJobState.jobRequestModel.assignedToUserId =
                                  driver!.userId!;
                              //---commented--bucket------
                              createJobState.jobRequestModel.bucket = 'NIL';

                              createJobState.refresh();
                            }
                          },
                          items:
                          isClient() 
        ? [clientSpotter].map<DropdownMenuItem<Driver>>((Driver driver) {
            return DropdownMenuItem<Driver>(
              value: driver,
              child: AppTxt(
                text: '${driver.firstName} ${driver.lastName}',
                fontWeight: FontWeight.bold,
              ),
            );
          }).toList():
                              //---commented--bucket------
                              (isDriver() || isSpotter() || isGuard())
                                  ?
                                  //---commented---bucket-----
                                  createJobState.driversList.list
                                      .map<DropdownMenuItem<Driver>>(
                                          (Driver driver) {
                                      print('>>>>list>>>>>$driver');
                                      return DropdownMenuItem<Driver>(
                                        value: driver,
                                        child: AppTxt(
                                          text:
                                              '${driver.firstName} ${driver.lastName}',
                                          fontWeight: FontWeight.bold,
                                        ),
                                      );
                                    }).toList()

                                  //---commented---bucket-----
                                  : sortDrivers(createJobState.driversList.list)
                                      .map<DropdownMenuItem<Driver>>(
                                          (Driver driver) {
                                      print('>>>>list>>>>>$driver');
                                      print('>>>>list>>>>>${driver.firstName}');

                                      return DropdownMenuItem<Driver>(
                                        value: driver,
                                        child: AppTxt(
                                          text:
                                              '${driver.firstName} ${driver.lastName}',
                                          fontWeight: FontWeight.bold,
                                        ),
                                      );
                                    }).toList()

                          //---commented---bucket-----
                          // : dropdownItems
                          //                      .map((item) {

                          //   print('>>>>list>>>>>$item');
                          //                       return DropdownMenuItem<Driver>(
                          //     value: item.value,
                          //     child: AppTxt(
                          //       text: '${item.value?.firstName}', // Example of modifying the display text
                          //       fontWeight: FontWeight.bold,
                          //     ),
                          //   );
                          // }).toList(),

                          // items: dropdownItems.map<DropdownMenuItem<Driver>>((Driver driver) {
                          //   return DropdownMenuItem<Driver>(
                          //     value: driver,
                          //     child: AppTxt(
                          //       text: '${driver.firstName} ${driver.lastName}',
                          //       fontWeight: FontWeight.bold,
                          //     ),
                          //   );
                          // }).toList(),
                          ),
                    ),
                    const SizedBox(height: 20),
                    const AppTxt(
                      text: 'Job Priorities',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    const SizedBox(height: 10),
                    _jobPriorities(context, createJobState),
                    const SizedBox(height: 20),
                    AppBtn(
                      text: 'CREATE SPOT',
                      bgColor: Theme.of(context).primaryColor,
                      color: Colors.white,
                      loading: createJobState.createJobLoading,
                      height: 40,
                      onPress: () async {
                        if(isClient())
                        {
                              createJobState.jobRequestModel.assignedToUserId =
                                  '';
                                  createJobState.selectedDriver=clientSpotter;
                              createJobState.jobRequestModel.bucket = 'BUCKET_SPOTTER' ?? '';
                              // createJobState.refresh();
                        }
                        //---commented--bucket------
                        (isDriver() || isSpotter()) ? driverCheck() : '';
                        // print('risaj------------${createJobState.jobRequestModel.bucket}');
                        //----commented

                        if (null == createJobState.selectedPickUpLocation) {
                          showSnackBar('Please select Pick up Location',
                              success: false);
                          return;
                        }
                        if (null == createJobState.selectedFleetStatus) {
                          showSnackBar('Please select status', success: false);
                          return;
                        }
                        if (null == createJobState.selectedPickupSpot) {
                          showSnackBar('Please select Pick up Spot',
                              success: false);
                          return;
                        }
                        if (null == createJobState.selectedDropLocation) {
                          showSnackBar('Please select Drop Location',
                              success: false);
                          return;
                        }
                        if (null == createJobState.selectedTruckDetail) {
                          if (createJobState
                              .autoCompleteController.text.isNotEmpty) {
                            showSnackBar(
                              'Invalid Unit/Trailer #. Please verify and enter the correct trailer/unit #',
                              success: false,
                            );
                            return;
                          } else {
                            showSnackBar('Please select Trailer',
                                success: false);
                            return;
                          }
                        }
                        if (null == createJobState.selectedDriver) {
                          showSnackBar('Please select a Driver',
                              success: false);
                          return;
                        }
                        print('risaj---');

                        createJobState.createJob();
                      },
                    ),
                    const SizedBox(height: 60),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  _jobPriorities(BuildContext context, CreateJobState createJobState) {
    return Row(
      children: _getJobPriorities(context)
          .map(
            (e) => JobPrioriity(
              text: e.text.sentenceCase() ?? '',
              val: e.text.toUpperCase(),
              groupVal: createJobState.jobRequestModel.priority.toUpperCase(),
              color: e.color,
              onChanged: (String priority) async {
                createJobState.jobRequestModel.priority = priority;
                createJobState.refresh();
              },
            ),
          )
          .toList(),
    );
  }

  List<Priority> _getJobPriorities(BuildContext context) {
    LoginResponseModel? loginResponseModel = Preferences.loginResponseModel;
    if (null == loginResponseModel || loginResponseModel.roles.isEmpty) {
      return [];
    }
    String role = loginResponseModel.roles.first.toLowerCase();
    if (role.contains('guard') ||
        role.contains('spotter') ||
        role.contains('admin')) {
      return [
        Priority(
          text: 'HIGH',
          color: Colors.red,
        ),
        Priority(
          text: 'MEDIUM',
          color: Theme.of(context).primaryColor,
        ),
        Priority(
          text: 'LOW',
          color: Colors.grey,
        )
      ];
    }
    return [
      Priority(
        text: 'HIGH',
        color: Colors.red,
      )
    ];
  }

  spot.SpotOnLocation? getLocationsValue(
      spot.SpotOnLocation? spot, CreateJobState createJobState) {
    // print("qqqqq ${supplier?.id}");
    // print('jino--1');
    for (var val in createJobState.spotOnLocationList.list ?? []) {
      print('jino--12${createJobState.selectedPickUpLocation?.locationId}');

      // if (spot?.locationId == createJobState.selectedPickUpLocation?.locationId) {
      if (val?.locationId == spot?.locationId) {
        // print(val.locationId);
        createJobState.jobRequestModel.pickupLocationId = val!.locationId!;
        print('jino--136${createJobState.jobRequestModel.pickupLocationId}');

        // return createJobState.spotOnLocationList.list.first;
        return val;
      }
    }

    return null;
  }

  sp.Spot? getSpotValue(sp.Spot? spot, CreateJobState createJobState) {
    // print("qqqqq ${supplier?.id}");
    print('jin--1');
    // print('jin--2${createJobState.pickupSpotsList.list}');

    for (var val in createJobState.pickupSpotsList.list ?? []) {
      print('jin--2${createJobState.pickupSpotsList.list}');
      print('jin--20${selectedSPOT}');

      // if (spot?.locationId == createJobState.selectedPickUpLocation?.locationId) {
      if (val?.spotId == selectedSPOT) {
        createJobState.selectedPickupSpot = sp.Spot(
          locationId: val.locationId,
          spotId: val.spotId,
          spotName: val.spotName,
          type: val.type,
          status: 'Available',
          latitude: 34.0522,
          longitude: -118.2437,
          remarks: 'Near the entrance',
          isActive: true,
          fleet: null,
          audit: null,
        );
        createJobState.jobRequestModel.pickupSpotId =
            createJobState.selectedPickupSpot?.spotId;
        print('jin--13----${createJobState.selectedPickupSpot?.spotId}');

        print('jin--14----${createJobState.jobRequestModel.pickupSpotId}');

        // return createJobState.selectedPickupSpot;
        return val;
      }
    }

    return createJobState.selectedPickupSpot;
  }

  List<Driver> sortDrivers(List<Driver> drivers) {
    // Define the special drivers to prioritize
    const specialDriver1 = 'BUCKET SYSTEM DRIVER';
    const specialDriver2 = 'BUCKET SYSTEM SPOTTER';

    // Define the custom sorting function
    drivers.sort((a, b) {
      // Check if 'a' is the special driver or not
      int priorityA = (a.firstName == specialDriver1)
          ? 1
          : (a.firstName == specialDriver2)
              ? 2
              : 3;

      // Check if 'b' is the special driver or not
      int priorityB = (b.firstName == specialDriver1)
          ? 1
          : (b.firstName == specialDriver2)
              ? 2
              : 3;

      // Sort based on priority
      return priorityA.compareTo(priorityB);
    });

    // Filter the sorted list to include only those with 'rolename' as 'DRIVER'
    return drivers
        .where((driver) => driver.roles.any((roles) =>
            roles.roleName == 'DRIVER' ||
            roles.roleName == 'SPOTTER' ||
            roles.roleName == 'SUPERVISOR'))
        .toList();
  }
  // Define the special drivers to prioritize
  // const specialDriver1 = 'BUCKET_DRIVER';
  // const specialDriver2 = 'BUCKET_SPOTTER';

  // // Define the custom sorting function
  // drivers.sort((a, b) {
  //   // Check if 'a' is the special driver or not
  //   int priorityA = (a.firstName == specialDriver1) ? 1 :
  //                   (a.firstName == specialDriver2) ? 2 : 3;

  //   // Check if 'b' is the special driver or not
  //   int priorityB = (b.firstName == specialDriver1) ? 1 :
  //                   (b.firstName == specialDriver2) ? 2 : 3;

  //   // Sort based on priority
  //   return priorityA.compareTo(priorityB);
  // });

  // return drivers;
}

class Priority {
  String text;
  Color color;

  Priority({required this.text, required this.color});
}
