import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:spot_on/utils/app_logger.dart';
import 'package:spot_on/utils/gaps.dart';
import 'package:spot_on/utils/imports.dart';
import '../models/truck_list_model.dart';
import '../providers/damage_report_state.dart';

class DamageScreen extends StatefulWidget {
  const DamageScreen({Key? key}) : super(key: key);
  final String screenTitle = 'Damage';

  @override
  State<DamageScreen> createState() => _DamageScreenState();
}

class _DamageScreenState extends State<DamageScreen> {
  TextEditingController commentcontroller = TextEditingController();
  TextEditingController othercontroller = TextEditingController();
  dynamic notes;
  clearAll() {
    setState(() {
      _selecteddamage = null;
      notes = null;
    });
  }

  List<String> dropdownItems = ['Brakes', 'Flat Tire', 'Other', 'Holes In The Floor', 'VON Needed'];
  String? _selecteddamage;
  String? fle;
  String? com;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      DamageReportState damageReportState = context.read<DamageReportState>();
      damageReportState.clearAll();
    });
  }

  @override
  Widget build(BuildContext context) {
    DamageReportState damageReportState = context.read<DamageReportState>();

    return Scaffold(
      backgroundColor: appWhite,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          gapH32,
          const AppTxt(
            text: 'Trailer',
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
          TypeAheadField(
            textFieldConfiguration: TextFieldConfiguration(
              autofocus: false,
              decoration: const InputDecoration(
                hintText: "Search Trailer",
              ),
              controller: damageReportState.autoCompleteController,
            ),
            getImmediateSuggestions: false,
            hideOnEmpty: true,
            minCharsForSuggestions: 1,
            suggestionsCallback: (pattern) async {
              var result = await Services.getTruckListNew(pattern);
              if (result is Success) {
                var truckListModel = result.response as TruckListModel;
                return truckListModel.list.where(
                  (e) => e.unitNumber!.toLowerCase().contains(
                        pattern.toLowerCase(),
                      ),
                );
              }
              return [];
            },
            loadingBuilder: (context) {
              return const Center(
                child: SizedBox(
                  height: 16,
                  width: 16,
                  child: CircularProgressIndicator(strokeWidth: 1),
                ),
              );
            },
            itemBuilder: (context, suggestion) {
              TruckDetail? truckDetail = suggestion as TruckDetail;
              return Padding(
                padding: const EdgeInsets.all(10.0),
                child: AppTxt(
                  text: truckDetail.unitNumber?.replaceAll('-', ' ') ?? '',
                  color: truckDetail.isHotTrailer ? Colors.red : Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
            onSuggestionSelected: (suggestion) {
              TruckDetail? truckDetail = suggestion as TruckDetail;
              if (truckDetail == null) {
                return;
              }
              damageReportState.selectedTruckDetail = truckDetail;
              // damageReportState.damagemodel?.fleetId = truckDetail.fleetId!;
              damageReportState.autoCompleteController.text = truckDetail.unitNumber ?? '';
              setState(() {
                fle = truckDetail.fleetId!;
              });
              printLog('fleet--submit--${damageReportState.fleetId} ');
              printLog('fleet--unit--${truckDetail.unitNumber} ');

              damageReportState.refresh();
            },
          ),
          gapH32,

          //         const AppTxt(
          //   text: 'Trailer Status',
          //   fontSize: 14,
          //   fontWeight: FontWeight.bold,
          // ),
          // DropdownButton<FleetStatus>(
          //   hint: const AppTxt(text: 'Select Trailer Status'),
          //   value: damageReportState.selectedFleetStatus,
          //   icon: damageReportState.truckListLoading ? const CupertinoActivityIndicator() : const Icon(Icons.arrow_drop_down_outlined),
          //   iconSize: 24,
          //   elevation: 16,
          //   isExpanded: true,
          //   style: TextStyle(color: Theme.of(context).primaryColor),
          //   onChanged: (FleetStatus? fleetStatus) {
          //     damageReportState.selectedFleetStatus = fleetStatus;
          //     // createJobState.jobRequestModel.fleetStatus = fleetStatus!.id;
          //     damageReportState.refresh();
          //   },
          //   items: damageReportState.fleetStatuses.map<DropdownMenuItem<FleetStatus>>((FleetStatus value) {
          //     return DropdownMenuItem<FleetStatus>(
          //       value: value,
          //       child: AppTxt(
          //         text: value.name,
          //         fontWeight: FontWeight.bold,
          //       ),
          //     );
          //   }).toList(),
          // ),
          const AppTxt(
            text: 'Base info',
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          gapH16,
          DropdownButton<dynamic>(
            hint: const AppTxt(text: 'Out Of Service Reason :'),
            value: _selecteddamage,
            icon: const Icon(Icons.arrow_drop_down_outlined),
            iconSize: 24,
            elevation: 16,
            isExpanded: true,
            style: TextStyle(color: Theme.of(context).primaryColor),
            onChanged: (value) {
              setState(() {
                _selecteddamage = value;
                // damageReportState.damagemodel?.message=value.toString();
                print('----0-${value.toString()}');
              });
            },
            items: dropdownItems.map<DropdownMenuItem<String>>((String value) {
              return DropdownMenuItem<String>(
                  value: value,
                  child: AppTxt(
                    text: value,
                    fontWeight: FontWeight.bold,
                  ));
            }).toList(),
          ),
          _selecteddamage == 'Other' ? gapH16 : SizedBox(),
          _selecteddamage == 'Other'
              ? AppTFController(
                  controller: othercontroller,
                  hintText: "Damage Note",
                  initialValue: "",
                  onTextChanged: (val) {
                    setState(() {
                      notes = val;
                      // damageReportState.damagemodel?.note=val.toString();
                      print('${val.toString()}');
                    });
                  },
                )
              : SizedBox(),

          gapH24,
          const AppTxt(
            text: 'Status',
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          gapH16,
          AppTFController(
            controller: commentcontroller,
            hintText: "Comments",
            initialValue: '',
            onTextChanged: (val) {
              setState(() {
                // damageReportState.damagemodel?.comments=val;
                com = val.toString();
              });
            },
          ),

          gapH32,

          Row(
            children: [
              Expanded(
                child: AppBtn(
                    loading: damageReportState.isLoading,
                    bgColor: appErrorColor,
                    text: "RESET",
                    onPress: () {
                      print('------------resetted----------');
                      setState(() {
                        _selecteddamage = null;
                        commentcontroller.clear();
                        othercontroller.clear();
                        damageReportState.clearAll();
                      });
                    }),
              ),
              gapW16,
              Expanded(
                child: AppBtn(
                    loading: damageReportState.isLoading,
                    bgColor: appColor,
                    text: "SAVE",
                    onPress: () {
                      print('values-------${damageReportState.report}--1------${damageReportState.comment}------');
                      if (_selecteddamage == null || _selecteddamage == 'Other' && othercontroller.text.isEmpty || com == null) {
                        showToast("Fill all required fields!");
                        return;
                      }

                      DamageReportState().postDamage(_selecteddamage, com, fle);
                      setState(() {
                        _selecteddamage = null;
                        commentcontroller.clear();
                        othercontroller.clear();
                        damageReportState.clearAll();
                      });
                    }),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
