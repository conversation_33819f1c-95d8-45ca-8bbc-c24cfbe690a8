import 'package:spot_on/utils/imports.dart';

class ResetPwdScreen extends StatelessWidget {
  const ResetPwdScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    LoginState appState = context.watch<LoginState>();
    return Scaffold(
      backgroundColor: appColor,
      body: GlobalWidget(
        child: AbsorbPointer(
          absorbing: appState.loginLoading,
          child: Container(
            padding: const EdgeInsets.all(0.0),
            color: Colors.white,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                const Stack(
                  children: [
                    AppLogoHeader(),
                    Padding(
                      padding: EdgeInsets.only(top: 50.0),
                      child: AppCloseBtn(),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const AppTxt(
                        text: 'Forgot Password',
                        fontWeight: FontWeight.bold,
                        fontSize: 23,
                      ),
                      const SizedBox(height: 30),
                      AppTF(
                        icon: Icons.email_outlined,
                        hintText: 'Email',
                        onTextChanged: (val) {
                          appState.email = val;
                        },
                      ),
                      const SizedBox(height: 50),
                      AppBtn(
                        loading: appState.resetPwdLoading,
                        bgColor: Theme.of(context).primaryColor,
                        color: Colors.white,
                        loadingColor: Colors.white,
                        text: 'SUBMIT',
                        onPress: () async {
                          if (appState.email.isEmpty) {
                            showSnackBar('Please enter a valid Email.', success: false);
                            return false;
                          }
                          if (!Utils.isValidEmail(appState.email)) {
                            showSnackBar('Please enter a valid Email.', success: false);
                            return false;
                          }
                          appState.doResetPassword();
                        },
                      ),
                      const SizedBox(height: 30.0),
                      // const AppTxt(
                      //   text: 'You will get OTP in registered email address',
                      //   alignment: TextAlign.center,
                      // ),
                      // const SizedBox(height: 30.0),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
