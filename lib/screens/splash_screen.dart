// ignore_for_file: non_constant_identifier_names

import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:package_info/package_info.dart';
import 'package:spot_on/app_location.dart';
import 'package:spot_on/models/cliient_list_model.dart';
import 'package:spot_on/providers/create_job_state.dart';
import 'package:spot_on/providers/jobs_state.dart';
import 'package:spot_on/utils/bol_dvir_status.dart';
import 'package:spot_on/utils/gaps.dart';
import 'package:spot_on/utils/imports.dart';
import 'package:spot_on/utils/push_notifications.dart';
import 'package:url_launcher/url_launcher.dart';

import '../models/forceupdatemodel.dart';
import '../utils/app_logger.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  ///
  @override
  void initState() {
    super.initState();
    getVersionDetails();
    // _init();
  }

  getVersionDetails() async {
    print('ver-----------');

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    print('version00------${packageInfo.buildNumber}-----------');
    print('version00------${packageInfo.version}-----------');
    double version = double.parse(Platform.isIOS ? packageInfo.version : packageInfo.buildNumber);
    print('version000000------${version}-----------');

    if (packageInfo.version.length > 4) {
      print('ver-----------');

      double NewVersion = double.parse(packageInfo.version.substring(0, packageInfo.version.length - 2));
      CheckLatestVersion(NewVersion);
    } else {
      CheckLatestVersion(version);
      print('ver------3-----');
    }
  }

  CheckLatestVersion(double version) async {
    var os = Platform.isIOS ? 'ios' : 'android';
    print('ver------enter-----');
    var ver = version.toString();
    var url = "${Services.baseUrl}/v1/mobile/version/status?currentVersion=$ver&OS=$os";
    print('---ooop-----$url');
    try {
      Response response = await Dio(BaseOptions()).get(
        url,
      );
      print('---ooop--st---${response.statusCode}');

      if (response.statusCode == 200) {
        print('version response-----${response.data.toString()}');
        var jsonResponse = ForceUpdateModel.fromJson(response.data);

        print(version);
        // print(CurrentVersion);
        print('----new---$version');
        print('----new-version--${jsonResponse.forceUpdate}');
        bool? update = null;

        // print('----corrent---$CurrentVersion');
        if (Platform.isIOS) {
          print('----corrent---');

          setState(() {
            update = jsonResponse.forceUpdate ?? false;
          });
        } else {
          print('----corrent---');

          setState(() {
            update = jsonResponse.forceUpdate ?? false;
          });
        }
        // setState(() {
        //   update = jsonResponse.forceUpdate ?? false;
        // });
        if (update == true) {
          print('kkkkk--$update');
          // getData();
          if (Platform.isIOS) {
            _showVersionDialog(context, 'https://apps.apple.com/in/app/a-blair-spot-on/id1622749371');
          } else if (Platform.isAndroid) {
            _showVersionDialog(context, 'https://play.google.com/store/apps/details?id=com.ablair.spoton');
          }
        } else {
          print('kkkkkfalse--$update');

          setState(() {
            _init();
          });
        }
      } else {
        print('Error--not-responding--');
        throw Exception("Server not responding");
      }
    } catch (e) {

      print('Error-----  ${e.toString()}');

      throw Exception("Server not responding");
    }
  }

  //Show Dialog to force user to update
  _showVersionDialog(context, storelink) async {
    await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        String title = "New Update Available";
        String message = "There is a newer version of the app available. Please update it now.";
        String btnLabel = "Update Now";
        String btnLabelCancel = "Later";
        return Platform.isIOS
            ? AlertDialog(
                title: Text(
                  title,
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                content: Text(message),
                actions: <Widget>[
                  // CupertinoAlertDialog(
                  //     title: Text(title),
                  //     content: Text(message),
                  //     actions: <Widget>[

                  AppBtn(
                      bgColor: Theme.of(context).primaryColor,
                      // color: Colors.white,
                      onPress: () {
                        Uri StoreUrl = Uri.parse(storelink);
                        _launchURL(StoreUrl);
                      },
                      // Handle button press
                      text: btnLabel),
                  // TextButton(
                  //   onPressed: () {
                  //     //write onPressed function here
                  //     Uri StoreUrl = Uri.parse(storelink);
                  //     _launchURL(StoreUrl);
                  //   },
                  //   child: Text(btnLabel,style: TextStyle(color: Colors.black),),
                  // ),
                  // TextButton(
                  //   onPressed: () {
                  //     Navigator.pop(context);
                  //     Future.delayed(const Duration(milliseconds: 1000), () {
                  //       getVersionDetails();
                  //     });
                  //   },
                  //   child: Text(btnLabelCancel),
                  // ),
                ],
              )
            : AlertDialog(
                title: Text(
                  title,
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                content: Text(message),
                actions: <Widget>[
                  AppBtn(
                      bgColor: Theme.of(context).primaryColor,
                      // color: Colors.white,
                      onPress: () {
                        Uri StoreUrl = Uri.parse(storelink);
                        _launchURL(StoreUrl);
                      },
                      // Handle button press
                      text: btnLabel),

                  //         OutlinedButton(
                  //   onPressed: () {
                  //     // Handle button press
                  //     print('Button Pressed');
                  //   },
                  //   style: OutlinedButton.styleFrom(
                  //     side: BorderSide(
                  //       color: Colors.black, // Border color
                  //       width: 2.0, // Border width
                  //     ),
                  //   ),
                  //   child: Text(btnLabel,style: TextStyle(color: Colors.black)),
                  // ),
                  // eleva(
                  //   onPressed: () {
                  //     Uri StoreUrl = Uri.parse(storelink);
                  //     _launchURL(StoreUrl);
                  //   },
                  //   child: Text(btnLabel,style: TextStyle(color: Colors.black),),
                  // ),
                  // TextButton(
                  //   onPressed: () {
                  //     Navigator.pop(context);
                  //     Future.delayed(const Duration(milliseconds: 1000), () {
                  //       getVersionDetails();
                  //     });
                  //   },
                  //   child: Text(btnLabelCancel),
                  // ),
                ],
              );
      },
    );
  }

  _launchURL(Uri url) async {
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      throw 'Could not launch $url';
    }
  }

  _init() async {
    await Future.delayed(const Duration(seconds: 3));
    await Preferences.init();
    BolDvirStatus().reset().then((value) {
      HomeState homeState = globalKey.currentContext!.read<HomeState>();
      homeState.setDashboardTabs(Preferences.loginResponseModel!.roles.first);
    });
    if (!Preferences.isLoggedIn()) {
      openLogin();
      return;
    }
    AppLocation().requestLocationService();
    SpotOnClient? client = await Preferences.getSelectedClient();
    if (null == client) {
      LoginState loginState = context.read<LoginState>();
      await loginState.getClients();
      if (loginState.clientListModel.list.length == 1) {
        Preferences.saveSelectedClient(loginState.clientListModel.list.first);
        openHome();
        JobsState jobsState = context.read<JobsState>();
        getJobsForHomeTab();
        jobsState.getJobs();
        CreateJobState createJobState = context.read<CreateJobState>();
        createJobState.getSpotOnLocations();
        createJobState.getTruckList();
        createJobState.getEntryExitReportList();
        createJobState.selectedCarrier = createJobState.carrierList.first;
        HomeState homeState = globalKey.currentContext!.read<HomeState>();
        homeState.setDashboardTabs(Preferences.loginResponseModel!.roles.first);
        if (await homeState.askPushPermission()) {
          await homeState.registerDevice();
          await FBPushNotification.initListeners();
        }
        return;
      }
      openClientListScreen();
      return;
    }
    registerDevice();
    openHome();
    JobsState jobsState = context.read<JobsState>();
    getJobsForHomeTab();
    jobsState.getJobs();
    CreateJobState createJobState = context.read<CreateJobState>();
    createJobState.getSpotOnLocations();
    createJobState.getTruckList();
    createJobState.getEntryExitReportList();
    createJobState.selectedCarrier = createJobState.carrierList.first;
    HomeState homeState = globalKey.currentContext!.read<HomeState>();
    homeState.setDashboardTabs(Preferences.loginResponseModel!.roles.first);
    if (await homeState.askPushPermission()) {
      await homeState.registerDevice();
      FBPushNotification.initListeners();
    }
  }

  void registerDevice() async {
    try {
      Services.registerDevice(
        deviceModel: '',
        deviceName: '',
        deviceRegistrationId: await FBPushNotification.getToken(),
        deviceType: Platform.isIOS ? 'IPHONE' : 'ANDROID',
      );
      await Future.delayed(const Duration(seconds: 2));
      Services.registerDeviceNew(
        deviceModel: '',
        deviceName: '',
        deviceRegistrationId: await FBPushNotification.getToken(),
        deviceType: Platform.isIOS ? 'IPHONE' : 'ANDROID',
      );
    } catch (e) {
      printLog(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: btnColor1,
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        backgroundColor: btnColor1,
        systemOverlayStyle: const SystemUiOverlayStyle(
          systemNavigationBarColor: appBg,
          statusBarColor: appBg,
          statusBarIconBrightness: Brightness.light,
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: double.infinity,
        color: appBg,
        child: Column(
          children: [
            const Spacer(),
            Image.asset(
              'assets/images/logo_new.png',
              width: MediaQuery.of(context).size.width * 0.8,
            ),
            gapH16,
            Preferences.isLoggedIn()
                ? const Text(
                    "Let's go to work!",
                    style: TextStyle(color: Colors.white),
                  )
                : const SizedBox.shrink(),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}
