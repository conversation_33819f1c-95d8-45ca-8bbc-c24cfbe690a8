import 'package:flutter/cupertino.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:spot_on/providers/trailer_audit_state.dart';
import 'package:spot_on/utils/gaps.dart';
import 'package:spot_on/utils/imports.dart';

import '../models/spot_on_locations.dart';
import '../models/spots_list.dart';
import '../models/truck_list_model.dart';
import '../providers/create_job_state.dart';
import '../widgets/add_trailer.dart';

class TrailerAuditScreen extends StatefulWidget {
  const TrailerAuditScreen({Key? key}) : super(key: key);

  final String screenTitle = "Trailer Audit";

  @override
  State<TrailerAuditScreen> createState() => _TrailerAuditScreenState();
}

class _TrailerAuditScreenState extends State<TrailerAuditScreen> {
  var controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      TrailerAuditState trailerAuditState = context.read<TrailerAuditState>();
      trailerAuditState.clearAll();
      trailerAuditState.getTrailerAudit();
      trailerAuditState.getSpotOnLocations();
      trailerAuditState.refresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    TrailerAuditState trailerAuditState = context.watch<TrailerAuditState>();
    var items = trailerAuditState.trailerAudit?.list ?? [];
    return Scaffold(
      body: ModalProgressHUD(
        inAsyncCall: trailerAuditState.isLoading,
        opacity: 0,
        color: Colors.white,
        child: SizedBox(
          height: MediaQuery.of(context).size.height,
          child: ListView(
            children: [
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.transparent),
                  borderRadius: BorderRadius.circular(0),
                ),
                padding: const EdgeInsets.all(4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const AppTxt(
                      text: 'Trailer',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    TypeAheadField(
                      textFieldConfiguration: TextFieldConfiguration(
                        autofocus: false,
                        decoration: const InputDecoration(
                          hintText: "Search Trailer",
                        ),
                        controller: trailerAuditState.autoCompleteController,
                      ),
                      getImmediateSuggestions: false,
                      hideOnEmpty: true,
                      minCharsForSuggestions: 1,
                      suggestionsCallback: (pattern) async {
                        var result = await Services.getTruckListNew(pattern);
                        if (result is Success) {
                          var truckListModel = result.response as TruckListModel;
                          return truckListModel.list.where(
                            (e) => e.unitNumber!.toLowerCase().contains(
                                  pattern.toLowerCase(),
                                ),
                          );
                        }
                        return [];
                      },
                      loadingBuilder: (context) {
                        return const Center(
                          child: SizedBox(
                            height: 16,
                            width: 16,
                            child: CircularProgressIndicator(strokeWidth: 1),
                          ),
                        );
                      },
                      itemBuilder: (context, suggestion) {
                        TruckDetail? truckDetail = suggestion as TruckDetail;
                        return Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: AppTxt(
                            text: truckDetail.unitNumber?.replaceAll('-', ' ') ?? '',
                            color: truckDetail.isHotTrailer ? Colors.red : Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      },
                      onSuggestionSelected: (suggestion) {
                        TruckDetail? truckDetail = suggestion as TruckDetail;
                        if (truckDetail == null) {
                          return;
                        }
                        trailerAuditState.selectedTruckDetail = truckDetail;
                        trailerAuditState.carrier = truckDetail.carrier;
                        trailerAuditState.autoCompleteController.text = truckDetail.unitNumber ?? '';
                        trailerAuditState.refresh();
                      },
                    ),
                    gapH12,
                    const AddTrailer(),
                    // gapH4,
                    AppTF(
                      key: Key(trailerAuditState.carrier ?? ""),
                      hintText: "Carrier (Editable)",
                      initialValue: trailerAuditState.carrier ?? "",
                      enabled: true,
                      onTextChanged: (val) {
                        trailerAuditState.carrier = val;
                      },
                    ),
                    gapH24,
                    const AppTxt(
                      text: 'Area',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    DropdownButton<SpotOnLocation>(
                      hint: const AppTxt(text: 'Select Area'),
                      value: trailerAuditState.selectedPickUpLocation,
                      icon: trailerAuditState.isLoading ? const CupertinoActivityIndicator() : const Icon(Icons.arrow_drop_down_outlined),
                      iconSize: 24,
                      elevation: 16,
                      isExpanded: true,
                      style: TextStyle(color: Theme.of(context).primaryColor),
                      onChanged: (SpotOnLocation? spotOnLocation) {
                        if (trailerAuditState.isLoading) {
                          return;
                        }
                        trailerAuditState.selectedPickupSpot = null;
                        trailerAuditState.selectedPickUpLocation = spotOnLocation;
                        trailerAuditState.area = spotOnLocation?.locationName;
                        trailerAuditState.getSpots(locationId: spotOnLocation!.locationId!, drop: false);
                        print('uuid----loc----==${spotOnLocation.locationId}');
                        print('area--------==${spotOnLocation.locationId}');

                        trailerAuditState.refresh();
                      },
                      items: trailerAuditState.spotOnLocationList.list.map<DropdownMenuItem<SpotOnLocation>>((SpotOnLocation value) {
                        return DropdownMenuItem<SpotOnLocation>(
                          value: value,
                          child: AppTxt(
                            text: value.locationName ?? '',
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      }).toList(),
                    ),
                    gapH16,
                    const AppTxt(
                      text: 'Slot',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    DropdownButton<Spot>(
                      hint: const AppTxt(text: 'Select Slot'),
                      value: trailerAuditState.selectedPickupSpot,
                      icon: trailerAuditState.isLoading ? const CupertinoActivityIndicator() : const Icon(Icons.arrow_drop_down_outlined),
                      iconSize: 24,
                      elevation: 16,
                      isExpanded: true,
                      style: const TextStyle(color: appBg),
                      onChanged: (Spot? spot) {
                        if (trailerAuditState.isLoading) {
                          return;
                        }
                        print('spot------${spot?.spotId}');
                        trailerAuditState.spotid = spot?.spotId;

                        trailerAuditState.selectedPickupSpot = spot;
                        trailerAuditState.slot = spot?.spotName;
                        trailerAuditState.refresh();
                      },
                      items: trailerAuditState.pickupSpotsList.list.map<DropdownMenuItem<Spot>>((Spot spot) {
                        String status = getSpotIdStatus(spot);
                        return DropdownMenuItem<Spot>(
                          value: spot,
                          child: Column(
                            children: [
                              const SizedBox(height: 20),
                              Row(
                                children: [
                                  const SizedBox(width: 5),
                                  AppTxt(
                                    text: '${spot.spotName}',
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const SizedBox(width: 10),
                                  const Spacer(),
                                  // AppTxt(
                                  //   text: status,
                                  //   color: appBg,
                                  //   fontSize: 14,
                                  // ),
                                  const SizedBox(width: 10),
                                ],
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                    gapH16,
                    const AppTxt(
                      text: 'Trailer Status',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    DropdownButton<FleetStatus>(
                      hint: const AppTxt(text: 'Select Trailer Status'),
                      value: trailerAuditState.selectedFleetStatus,
                      icon: trailerAuditState.isLoading ? const CupertinoActivityIndicator() : const Icon(Icons.arrow_drop_down_outlined),
                      iconSize: 24,
                      elevation: 16,
                      isExpanded: true,
                      style: TextStyle(color: Theme.of(context).primaryColor),
                      onChanged: (FleetStatus? fleetStatus) {
                        trailerAuditState.selectedFleetStatus = fleetStatus;
                        trailerAuditState.trailerStatus = fleetStatus?.name;
                        trailerAuditState.refresh();
                      },
                      items: trailerAuditState.fleetStatuses.map<DropdownMenuItem<FleetStatus>>((FleetStatus value) {
                        return DropdownMenuItem<FleetStatus>(
                          value: value,
                          child: AppTxt(
                            text:value.name=='Full'?'Loaded': value.name,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      }).toList(),
                    ),
                    gapH12,
                    AppTF(
                      hintText: "Notes (Optional)",
                      initialValue: trailerAuditState.notes ?? "",
                      onTextChanged: (val) {
                        trailerAuditState.notes = val;
                      },
                    ),
                    gapH32,
                    AppBtn(
                        // loading: trailerAuditState.isLoading,
                        bgColor: appColor,
                        text: "SAVE",
                        onPress: () {
                          if (trailerAuditState.selectedTruckDetail == null ||
                              trailerAuditState.area == null ||
                              trailerAuditState.slot == null ||
                              trailerAuditState.spotid == null ||
                              trailerAuditState.trailerStatus == null) {
                            showToast("Fill all required fields!");
                            return;
                          }
                          trailerAuditState.postTrailerAudit();
                        }),
                    gapH16,
                  ],
                ),
              ),
              gapH12,
              const Divider(
                thickness: 4,
                height: 4,
              ),
              gapH32,
              const AppTxt(
                text: 'Trailer Audit List',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              gapH24,
              items.isNotEmpty
                  ? ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: items.length,
                      itemBuilder: (context, index) {
                        var item = items[index];
                        // return ListTile(
                        //   title: Text(items?[index].area ?? "sbdh"),
                        // );
                        return Padding(
                          padding: const EdgeInsets.all(0),
                          child: Material(
                            elevation: 0,
                            clipBehavior: Clip.hardEdge,
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(0),
                            child: Container(
                              padding: const EdgeInsets.all(0),
                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(0)),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    decoration: const BoxDecoration(
                                      color: Colors.green,
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(6.0),
                                      ),
                                    ),
                                    padding: const EdgeInsets.all(10),
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: AppTxt(
                                            text: 'Trailer Audit - ${index + 1}',
                                            color: Colors.white,
                                            lines: 2,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  gapH16,
                                  const Row(
                                    children: [
                                      Icon(
                                        Icons.location_on_outlined,
                                        size: 16,
                                      ),
                                      gapW4,
                                      AppTxt(
                                        text: 'Area',
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 20.0, top: 4),
                                    child: AppTxt(text: item.area ?? ''),
                                  ),
                                  gapH16,
                                  const Row(
                                    children: [
                                      Icon(
                                        Icons.dashboard_outlined,
                                        size: 16,
                                      ),
                                      gapW4,
                                      AppTxt(
                                        text: 'Slot',
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 20.0, top: 4),
                                    child: AppTxt(text: item.slot ?? ''),
                                  ),
                                  gapH16,
                                  const Row(
                                    children: [
                                      Icon(
                                        Icons.local_shipping_outlined,
                                        size: 16,
                                      ),
                                      gapW4,
                                      AppTxt(
                                        text: 'Carrier',
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 20.0, top: 4),
                                    child: AppTxt(text: item.carrier ?? ''),
                                  ),
                                  gapH16,
                                  const Row(
                                    children: [
                                      Icon(
                                        Icons.local_shipping_outlined,
                                        size: 16,
                                      ),
                                      gapW4,
                                      AppTxt(
                                        text: 'Trailer',
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 20.0, top: 4),
                                    child: AppTxt(text: item.trailerNumber ?? ''),
                                  ),
                                  gapH16,
                                  const Row(
                                    children: [
                                      Icon(
                                        Icons.description_outlined,
                                        size: 16,
                                      ),
                                      gapW4,
                                      AppTxt(
                                        text: 'Trailer Status',
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 20.0, top: 4),
                                    child: AppTxt(text: item.trailerStatus ?? ''),
                                  ),
                                  gapH16,
                                  const Row(
                                    children: [
                                      Icon(
                                        Icons.description_outlined,
                                        size: 16,
                                      ),
                                      gapW4,
                                      AppTxt(
                                        text: 'Notes',
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 20.0, top: 4),
                                    child: AppTxt(text: item.notes ?? '-'),
                                  ),
                                  gapH16,
                                  const Row(
                                    children: [
                                      Icon(
                                        Icons.alarm_on_outlined,
                                        size: 16,
                                      ),
                                      gapW4,
                                      AppTxt(
                                        text: 'Created At',
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 20.0, top: 4),
                                    child: AppTxt(text: item.audit?.createdDate ?? ''),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return gapH32;
                      },
                    )
                  : const SizedBox(
                      height: 96,
                      child: Center(
                        child: AppTxt(
                          text: "No trailer audit found!",
                        ),
                      ),
                    ),
            ],
          ),
        ),
      ),
      backgroundColor: Colors.white,
    );
  }
}
