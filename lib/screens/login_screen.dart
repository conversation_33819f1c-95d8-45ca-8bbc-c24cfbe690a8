import 'package:flutter/services.dart';
import 'package:spot_on/utils/imports.dart';
import 'package:spot_on/widgets/app_info.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    LoginState appState = context.watch<LoginState>();
    return Scaffold(
      backgroundColor: appColor,
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: appBg,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.white,
        ),
      ),
      body: GlobalWidget(
        child: AbsorbPointer(
          absorbing: appState.loginLoading,
          child: Container(
            padding: const EdgeInsets.all(0.0),
            color: Colors.white,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                const AppLogoHeader(),
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const AppTxt(
                        text: 'Login',
                        fontWeight: FontWeight.bold,
                        fontSize: 23,
                      ),
                      const SizedBox(height: 30),
                      AppTF(
                        icon: Icons.email_outlined,
                        hintText: 'Email',
                        // initialValue: '<EMAIL>',
                        // initialValue: '<EMAIL>',
                        onTextChanged: (val) {
                          appState.userName = val;
                        },
                      ),
                      const SizedBox(height: 10),
                      AppTF(
                        icon: Icons.lock_outline,
                        hintText: 'Password',
                        // initialValue: 'hamid123',
                        endIcon: IconButton(
                          icon: Icon(
                            appState.showPwd ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                          ),
                          onPressed: () async {
                            appState.setShowPwd(!appState.showPwd);
                          },
                        ),
                        obscureText: !appState.showPwd,
                        onTextChanged: (val) {
                          appState.password = val;
                        },
                      ),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          AppLinkBtn(
                            text: 'Forgot Password?',
                            onTap: () async {
                              openResetPwdScreen();
                            },
                            color: appBg,
                          ),
                        ],
                      ),
                      const SizedBox(height: 60),
                      AppBtn(
                        text: 'LOG IN',
                        loading: appState.loginLoading,
                        bgColor: Theme.of(context).primaryColor,
                        color: Colors.white,
                        height: 40,
                        onPress: () async {
                          appState.doLogin();
                        },
                      ),
                      // Spacer(),
                      const SizedBox(height: 10.0),
                      Visibility(
                        visible: appState.errorMessage.isNotEmpty,
                        child: Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: AppTxt(
                            text: appState.errorMessage,
                            fontWeight: FontWeight.normal,
                            color: appErrorColor,
                            fontSize: 16,
                            alignment: TextAlign.center,
                            lineHeight: 1.2,
                            lines: 2,
                          ),
                        ),
                      ),
                      const SizedBox(height: 30.0),
                      const AppInfo(),
                      const SizedBox(height: 30.0),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
