import 'package:spot_on/models/cliient_list_model.dart';
import 'package:spot_on/utils/imports.dart';

class ClientListScreen extends StatelessWidget {
  const ClientListScreen({
    Key? key,
    this.inAccountsScreen = false,
  }) : super(key: key);

  final bool inAccountsScreen;

  @override
  Widget build(BuildContext context) {
    LoginState loginState = context.watch<LoginState>();
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: AppTxt(
          text: loginState.clientsLoading ? 'Loading Clients...' : 'Select Client',
        ),
        leading: null,
        actions: [
          Visibility(
            visible: !inAccountsScreen && null != loginState.selectedClient,
            child: IconButton(
              padding: const EdgeInsets.all(15),
              onPressed: () async {
                openHome();
                Preferences.saveSelectedClient(loginState.selectedClient!);
              },
              icon: Icon(
                Icons.check,
                color: appBlack,
              ),
            ),
          )
        ],
      ),
      body: Container(
        padding: const EdgeInsets.all(0),
        color: Colors.white,
        child: Column(
          children: [
            Expanded(
              child: ListView.separated(
                separatorBuilder: (context, index) => const Divider(
                  endIndent: 30,
                  indent: 30,
                ),
                itemCount: loginState.clientListModel.list.length,
                itemBuilder: (context, index) {
                  SpotOnClient client = loginState.clientListModel.list[index];
                  return InkWell(
                    onTap: () async {
                      loginState.selectedClient = client;
                      for (SpotOnClient c in loginState.clientListModel.list) {
                        c.selected = c.clientId == client.clientId;
                      }
                      loginState.refresh();
                    },
                    child: Container(
                      padding: const EdgeInsets.fromLTRB(0, 10, 0, 10),
                      child: Row(
                        children: [
                          const Icon(Icons.person),
                          const SizedBox(width: 20),
                          AppTxt(
                            text: client.clientName ?? '',
                            fontWeight: client.selected ? FontWeight.bold : FontWeight.normal,
                            color: client.selected ? appGreen : Theme.of(context).primaryColor,
                            fontSize: client.selected ? 23 : 18,
                            lines: 1,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
