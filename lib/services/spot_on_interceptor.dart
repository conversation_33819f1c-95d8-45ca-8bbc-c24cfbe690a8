// import 'package:http_interceptor/http/interceptor_contract.dart';
// import 'package:http_interceptor/models/request_data.dart';
// import 'package:http_interceptor/models/response_data.dart';
// import 'package:http/http.dart' as http;
// import '../utils/app_logger.dart';
// import '../utils/preference_utils.dart';
//
// class SpotOnInterceptor implements InterceptorContract {
//
//   @override
//   Future<RequestData> interceptRequest({required RequestData data}) async {
//     try {
//       data.headers["Content-Type"] = "application/json";
//       data.headers["Authorization"] =
//           "BEARER ${Preferences.loginResponseModel!.accessToken}";
//     } catch (e) {
//       // printLog(e);
//     }
//     return data;
//   }
//
//   @override
//   Future<ResponseData> interceptResponse({required ResponseData data}) async {
//     // If the response code is 401 (Unauthorized)
//     if (data.statusCode == 401) {
//       // Call the /refresh-token API to get new tokens
//       final newTokens = await _getNewTokens();
//
//       // Create a new RequestData object with the updated headers
//       final newHeaders = {...?data.request?.headers};
//       newHeaders['Authorization'] = 'Bearer ${newTokens.accessToken}';
//
//       final requestData = RequestData(
//           baseUrl: data.request!.url,
//           method: data.request!.method,
//           headers: newHeaders,
//           body: data.request?.body);
//       // Retry the request with the new access token
//       final response = await http.makeRequest(requestData);
//
//       // Return the response
//       return response;
//     }
//
//     // If the response code is not 401, return the original response
//     return data;
//   }
// }
