// part of 'bol_upload_cubit.dart';
//
// abstract class BolUploadState extends Equatable {
//   const BolUploadState();
// }
//
// class Loading extends BolUploadState {
//   @override
//   List<Object> get props => [];
// }
//
// class Loaded extends BolUploadState {
//   @override
//   List<Object> get props => [];
// }
//
// class Message extends BolUploadState {
//   final String message;
//
//   const Message({required this.message});
//
//   @override
//   List<Object> get props => [];
// }
