// import 'package:bloc/bloc.dart';
// import 'package:equatable/equatable.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:spot_on/services/api.dart';
//
// part 'bol_upload_state.dart';
//
// class BolUploadCubit extends Cubit<BolUploadState> {
//   BolUploadCubit() : super(Loaded());
//   var api = Api();
//
//   Future<void> uploadImages(
//       {required List<XFile> images, required String jobId}) async {
//     emit(Loading());
//     try {
//       String response = await api.uploadBol(
//         images: images,
//         jobId: jobId,
//         isUnsigned: true,
//       );
//       if (response == "Success") {
//         emit(const Message(message: "BoL uploaded!"));
//       }
//       emit(Loaded());
//     } catch (e) {
//       emit(const Message(message: "Failed to upload <PERSON><PERSON>!"));
//       emit(Loaded());
//     }
//   }
// }
