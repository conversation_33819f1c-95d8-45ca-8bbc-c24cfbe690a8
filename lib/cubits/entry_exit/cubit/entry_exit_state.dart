part of 'entry_exit_cubit.dart';

class EntryExitState extends Equatable {
  const EntryExitState();

  @override
  List<Object> get props => [];
}

class EntryExitInitial extends EntryExitState {}

class SuccessCarrier extends EntryExitState {
  final List<String>? carrierdetails;

  const SuccessCarrier({this.carrierdetails});
  @override
  List<Object> get props => [];
}

class SuccessSupplier extends EntryExitState {
  final SupplierList? supplierdetails;

  const SuccessSupplier({this.supplierdetails});
  @override
  List<Object> get props => [];
}

class SuccessSpot extends EntryExitState {
  final SpotListeEntryExit? spotdetails;

  const SuccessSpot({required this.spotdetails});
  @override
  List<Object> get props => [];
}

class SuccessLocation extends EntryExitState {
  final LocationListeEntryExit? locdetails;

  const SuccessLocation({required this.locdetails});
  @override
  List<Object> get props => [];
}

class SuccessClient extends EntryExitState {
  final ClientListeEntryExit? clientdetails;

  const SuccessClient({required this.clientdetails});
  @override
  List<Object> get props => [];
}

class SuccessDriver extends EntryExitState {
  final UsersListeEntryExit? driverdetails;

  const SuccessDriver({required this.driverdetails});
  @override
  List<Object> get props => [];
}

class SuccessTrailer extends EntryExitState {
  final UsersListeEntryExit? driverdetails;

  const SuccessTrailer({required this.driverdetails});
  @override
  List<Object> get props => [];
}

class SuccessSubmit extends EntryExitState {
  final String msgs;
  const SuccessSubmit({required this.msgs});
  @override
  List<Object> get props => [];
}
class FailedSubmit extends EntryExitState {
  final String msgs;
  const FailedSubmit({required this.msgs});
  @override
  List<Object> get props => [];
}

class SuccessCreate extends EntryExitState {
  final String msgs;
  const SuccessCreate({required this.msgs});
  @override
  List<Object> get props => [];
}

class SuccessCreateTrailer extends EntryExitState {
  final List<TruckDetail> list;
  const SuccessCreateTrailer({required this.list});
  @override
  List<Object> get props => [];
}
class SuccessAutofill extends EntryExitState {
  final EntryExitTrailerAutofillModel list;
  const SuccessAutofill({required this.list});
  @override
  List<Object> get props => [];
}

class Message extends EntryExitState {
  final String? msg;

  Message({this.msg});
  @override
  List<Object> get props => [];
}

class FailedMessageSubmit extends EntryExitState {
  final String? msg;

  FailedMessageSubmit({this.msg});
  @override
  List<Object> get props => [];
}
