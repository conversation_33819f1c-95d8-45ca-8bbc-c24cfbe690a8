import 'package:flutter/widgets.dart';

// Horizontal Spacing
const SizedBox gapW2 = SizedBox(width: 2);
const SizedBox gapW4 = SizedBox(width: 4);
const SizedBox gapW8 = SizedBox(width: 8);
const SizedBox gapW12 = SizedBox(width: 12);
const SizedBox gapW16 = SizedBox(width: 16);
const SizedBox gapW24 = SizedBox(width: 24);
const SizedBox gapW32 = SizedBox(width: 32);
const SizedBox gapW48 = SizedBox(width: 48);

// Vertical Spacing
const SizedBox gapH2 = SizedBox(height: 2);
const SizedBox gapH4 = SizedBox(height: 4);
const SizedBox gapH6 = SizedBox(height: 6);
const SizedBox gapH8 = SizedBox(height: 8);
const SizedBox gapH12 = SizedBox(height: 12);
const SizedBox gapH16 = SizedBox(height: 16);
const SizedBox gapH24 = SizedBox(height: 24);
const SizedBox gapH32 = SizedBox(height: 32);
const SizedBox gapH48 = SizedBox(height: 48);
