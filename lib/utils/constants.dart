import 'package:flutter/material.dart';
import 'package:package_info/package_info.dart';

const String searchPetsHint = 'Search';

// App Base Colors
const Color linkColorCode = Color(0XFF2F468C);
const Color btnColor1 = Color(0XFF211E3B);

const String fontBronova = 'Bronova';

const Color appColor = Color(0XFF56FFB5);
const Color appBg = Color(0xFF363636);
const Color appWhite = Colors.white;
Color appGreen = Colors.green;
Color appErrorColor = Colors.red;
Color successColor = Colors.green;
Color appBlack = Colors.black54;
Color? grey300 = Colors.grey[300];
Color appGrey = Colors.grey;
Color? appFocusedGrey = Colors.grey[600];

Color splashTitleRed = Colors.red;

const int SNACKBAR_DELAY = 3; // seconds
const int homeSliderNextImageSlideTime = 6; // seconds
const double btnBorderRadius = 3.0;
const String outofStockTxt = 'Out of Stock';
const int defaultPopularProductsCount = 10;
const int maxFirstNameLength = 20;
const int maxLastNameLength = 20;
const int maxLocationLength = 30;
const int maxEmailLength = 50;
const int maxPostNameLength = 50;
const int maxPostDescLength = 300;

const String iOSAppStoreID = '100000';

const String loginBanner = 'assets/images/login_logo_bg.png';
const String logo = 'assets/images/logo_new.png';
const String signUpBanner = 'assets/images/img_signup.png';
const String resetPwdBanner = 'assets/images/img_verify.png';
const String verifyBanner = 'assets/images/img_verify.png';
const String contactUsBanner = 'assets/images/img_contact_us.png';

Brightness statusBarIconBrightness = Brightness.light;
PackageInfo? packageInfo;

GlobalKey<NavigatorState> globalKey = GlobalKey();
const String appName = 'Spot On';

// service codes
const int unauthorized = 401;
