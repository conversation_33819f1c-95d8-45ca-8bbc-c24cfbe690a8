import 'package:spot_on/utils/app_logger.dart';

import '../models/bol/bol_dvir.dart';
import '../services/api.dart';

class BolDvirStatus {
  static BolDvir? status = BolDvir();

  Future<void> reset() async {
    var api = Api();
    try {
      BolDvir response = await api.getBolDvirStatus();
      printLog(response.toJson());
      status = response;
    } catch (e) {
      printLog(e);
    }
  }
}
