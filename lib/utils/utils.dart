import 'dart:io';

import 'package:email_validator/email_validator.dart';
import 'package:flutter/cupertino.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart' show DateFormat, toBeginningOfSentenceCase;
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:spot_on/main.dart';
import 'package:spot_on/models/client_details_new_model.dart';
import 'package:spot_on/models/confirm_drop_spot_id_model.dart';
import 'package:spot_on/models/job/jobs_list_model.dart';
import 'package:spot_on/models/spots_list.dart';
import 'package:spot_on/providers/client_details_state.dart';
import 'package:spot_on/providers/create_job_state.dart';
import 'package:spot_on/providers/jobs_state.dart';
import 'package:spot_on/providers/trailer_stand_upload_state.dart';
import 'package:spot_on/utils/bol_dvir_status.dart';
import 'package:spot_on/utils/gaps.dart';
import 'package:spot_on/utils/imports.dart';
import 'package:spot_on/widgets/entry_exit_bottom_sheet_content.dart';

import '../models/spot_on_locations.dart';
import '../popups/ask_bol_dialog.dart';
import '../popups/confirm_doc_dialog.dart';
import '../providers/bol_upload_state.dart';

class Utils {
  //
  static void hideKeyboard(BuildContext context) async {
    FocusScope.of(context).requestFocus(FocusNode());
  }

  static bool isValidEmail(String email) {
    return email.trim().isNotEmpty && EmailValidator.validate(email.trim());
  }

  static bool isValidPhoneNumber(String phone) {
    return phone.trim().isNotEmpty && phone.trim().length == 10;
  }

  static String buildJobDetails(Job job) {
    List<String> details = [];

    if (job.description != null && job.description!.isNotEmpty) {
      details.add(job.description ?? "");
    }
    if (job.pickupNotes != null && job.pickupNotes!.isNotEmpty) {
      details.add(job.pickupNotes ?? "");
    }
    if (job.dropNotes != null && job.dropNotes!.isNotEmpty) {
      details.add(job.dropNotes ?? "");
    }

    return details.join('\n');
  }
}

showSnackBar(String message, {int delay = 6, bool success = true}) {
  hideSnackBar();
  var snackBar = SnackBar(
    content: AppTxt(
      text: message,
      color: Colors.white,
      lineHeight: 1.2,
      lines: 6,
    ),
    duration: Duration(seconds: delay),
    action: SnackBarAction(
      label: 'OK',
      textColor: Colors.white,
      onPressed: () async {
        //
      },
    ),
    backgroundColor: success ? appGreen : appErrorColor,
  );
  snackBarKey.currentState?.showSnackBar(snackBar);
}

// void showMyDialog() {
//   showDialog(
//     context: globalKey.currentContext!,
//     builder: (context) => Dialog(
//       shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(12.0)), //this right here
//       child: const SizedBox(
//         height: 300.0,
//         width: 300.0,
//         child: Center(
//           child: Text("Hi"),
//         ),
//       ),
//     ),
//   );
// }

hideSnackBar() async {
  snackBarKey.currentState?.hideCurrentSnackBar();
}

showToast(String message) async {
  Fluttertoast.showToast(
    msg: message,
    toastLength: Toast.LENGTH_LONG,
    gravity: ToastGravity.BOTTOM,
    timeInSecForIosWeb: 1,
    backgroundColor: const Color(0XFF211E3B),
    textColor: Colors.white,
    fontSize: 16.0,
  );
}

Color getJobPriorityColor(String priority) {
  switch (priority) {
    case 'HIGH':
      return Colors.red;
    case 'MEDIUM':
      return Colors.orange;
    default:
      return Colors.yellow;
  }
}

Color? getJobStatusColor(String jobStatus) {
  switch (jobStatus) {
    case 'COMPLETED':
      return Colors.green;
    case 'OPEN':
      return Colors.red;
    default:
      return const Color(0xFFFFAA00);
  }
}

String getHomeJobActionBtnText1(String jobStatus) {
  switch (jobStatus) {
    case 'COMPLETED':
      return 'DONE';
    case 'OPEN':
      return 'DROP';
    default:
      return 'PICK UP';
  }
}

String getHomeJobActionBtnText(String jobStatus) {
  switch (jobStatus) {
    case 'IN_TRANSIT':
      return 'DROP';
    default:
      return 'PICK UP';
  }
}

Color getHomeJobActionBtnColor(BuildContext context, String jobStatus) {
  switch (jobStatus) {
    case 'IN_TRANSIT':
      return Colors.orange;
    default:
      return Theme.of(context).primaryColor;
  }
}

String getJobUpdateStatusString(String jobStatus) {
  switch (jobStatus) {
    case 'IN_TRANSIT':
      return 'COMPLETED';
    default:
      return 'IN_TRANSIT';
  }
}

bool isToday(DateTime dateTime) {
  return DateTime.now().difference(dateTime).inDays == 0;
}

showTripUpdateBottomSheetPickUp({
  required Job job,
  required bool isPickUp,
  required Function onTextChange,
  required Function onConfirmTap,
  required Function onClose,
}) {
  var controller = TextEditingController();
  showModalBottomSheet(
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    isDismissible: false,
    context: globalKey.currentContext!,
    builder: (context) {
      CreateJobState createJobState = context.watch<CreateJobState>();
      BolUploadState bolUploadState = context.watch<BolUploadState>();
      var data = MediaQuery.of(context);
      return StatefulBuilder(
        builder:
            (BuildContext context, void Function(void Function()) setState) {
          return MediaQuery(
            data: data.copyWith(
              textScaleFactor: 1,
              devicePixelRatio: 1,
            ),
            child: Container(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              height: 424,
              clipBehavior: Clip.hardEdge,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: ModalProgressHUD(
                inAsyncCall: createJobState.spotsLoading ||
                    createJobState.spotOnLocationsLoading ||
                    createJobState.truckListLoading ||
                    createJobState.driverListLoading ||
                    createJobState.createJobLoading ||
                    createJobState.entryExitLoading ||
                    createJobState.entryExitReportLoading ||
                    createJobState.jobDropSpotUpdateLoading ||
                    createJobState.newLoading,
                opacity: 0.1,
                blur: 0.3,
                color: Colors.transparent,
                child: Column(
                  // crossAxisAlignment: CrossAxisAlignment.start,
                  // mainAxisSize: MainAxisSize.min,
                  // physics: const NeverScrollableScrollPhysics(),
                  children: [
                    // const SizedBox(height: 5),
                    Container(
                      color: appBg,
                      height: 64,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Spacer(flex: 3),
                          AppTxt(
                            text:
                                'CONFIRM ${getHomeJobActionBtnText(job.status!)}',
                            alignment: TextAlign.center,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          const Spacer(flex: 2),
                          IconButton(
                            onPressed: () async {
                              onClose();
                              closeScreen();
                            },
                            icon: const Icon(
                              Icons.clear_outlined,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(
                          left: 20,
                          right: 20,
                        ),
                        child: ListView(
                          shrinkWrap: true,
                          // crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // const Divider(color: Colors.black),
                            const SizedBox(height: 16),
                            const Row(
                              children: [
                                AppTxt(
                                  text: "Pick Up",
                                  fontSize: 14,
                                  color: appBg,
                                  fontWeight: FontWeight.bold,
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),
                            Padding(
                              padding: const EdgeInsets.only(left: 0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 5),
                                        AppTxt(
                                            text:
                                                '${job.pickupLocation?.locationName}'),
                                        const SizedBox(height: 5),
                                        AppTxt(
                                            text:
                                                '${job.pickupLocation?.street}'),
                                        const SizedBox(height: 5),
                                        AppTxt(
                                          text:
                                              '${job.pickupLocation?.city}, ${job.pickupLocation?.zip}',
                                        ),
                                        const SizedBox(height: 5),
                                      ],
                                    ),
                                  ),
                                  const Spacer(),
                                  const VerticalDivider(
                                    width: 1,
                                    color: Colors.black,
                                  ),
                                ],
                              ),
                            ),
                            Divider(
                              color: grey300,
                              thickness: 1,
                            ),
                            const SizedBox(height: 10),
                            Visibility(
                              visible: isPickUp,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const AppTxt(
                                    text: 'Select Pick Up Spot' ?? '',
                                    alignment: TextAlign.end,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const SizedBox(height: 5),
                                  Visibility(
                                    visible: isPickUp,
                                    child: DropdownButton<Spot>(
                                      hint: const AppTxt(
                                        text: 'Select Pick Up Spot',
                                        fontSize: 12,
                                      ),
                                      value: createJobState.selectedPickupSpot,
                                      icon: createJobState.spotsLoading ||
                                              createJobState
                                                  .jobDropSpotUpdateLoading
                                          ? const CupertinoActivityIndicator()
                                          : const Icon(
                                              Icons.arrow_drop_down_outlined),
                                      iconSize: 24,
                                      elevation: 16,
                                      isExpanded: true,
                                      style: const TextStyle(color: appBg),
                                      onChanged: (Spot? spot) {
                                        if (createJobState
                                            .spotOnLocationsLoading) {
                                          return;
                                        }
                                        if (null == job.pickupLocation) {
                                          return;
                                        }
                                        createJobState.selectedPickupSpot =
                                            spot;
                                        createJobState.jobRequestModel
                                            .pickupSpotId = spot!.spotId;
                                        createJobState.refresh();
                                      },
                                      items: createJobState.pickupSpotsList.list
                                          .map<DropdownMenuItem<Spot>>(
                                              (Spot spot) {
                                        String status = getSpotIdStatus(spot);
                                        return DropdownMenuItem<Spot>(
                                          value: spot,
                                          child: Column(
                                            children: [
                                              const SizedBox(height: 20),
                                              Row(
                                                children: [
                                                  const SizedBox(width: 5),
                                                  Expanded(
                                                    child: AppTxt(
                                                      text: '${spot.spotName}',
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  const SizedBox(width: 5),
                                                  AppTxt(
                                                    text: status,
                                                    color: appBg,
                                                    fontSize: 14,
                                                    lines: 2,
                                                  ),
                                                  const SizedBox(width: 10),
                                                ],
                                              ),
                                            ],
                                          ),
                                        );
                                      }).toList(),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 10),
                            TextField(
                              controller: controller,
                              decoration: InputDecoration(
                                hintText: "Pick up notes (if any)",
                                enabledBorder: UnderlineInputBorder(
                                    borderSide: BorderSide(color: grey300!)),
                                focusedBorder: UnderlineInputBorder(
                                    borderSide: BorderSide(color: grey300!)),
                                hintStyle:
                                    const TextStyle(color: appBg, fontSize: 14),
                                labelStyle: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                              ),
                              onChanged: (val) {
                                onTextChange(val);
                              },
                            ),
                            gapH8,
                            bolUploadState.imageList.isNotEmpty
                                ? Container(
                                    height: 80,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                    child: ListView.separated(
                                      scrollDirection: Axis.horizontal,
                                      itemCount:
                                          bolUploadState.imageList.length,
                                      itemBuilder: (context, index) {
                                        return Container(
                                          width: 56,
                                          height: double.infinity,
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(4)),
                                          clipBehavior: Clip.hardEdge,
                                          child: Image.file(
                                            File(bolUploadState
                                                .imageList[index].path),
                                            fit: BoxFit.cover,
                                          ),
                                        );
                                      },
                                      separatorBuilder:
                                          (BuildContext context, int index) {
                                        return gapW8;
                                      },
                                    ),
                                  )
                                : const SizedBox.shrink(),
                            gapH8,
                          ],
                        ),
                      ),
                    ),
                    gapH4,
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: AppBtn(
                        color: Colors.white,
                        bgColor: Theme.of(context).primaryColor,
                        text: 'CONFIRM ${getHomeJobActionBtnText(job.status!)}',
                        onPress: () async {
                          print('hi--------1');
                          if (!(isDriver() && isBolEnabled())) {
                            bolUploadState.allowToProceed();
                          }
                          if (!bolUploadState.proceed) {
                            if (isDriver() && isBolEnabled()) {
                              showDialog(
                                context: context,
                                routeSettings:
                                    const RouteSettings(name: "popup"),
                                builder: (context) {
                                  return AskBolDialog(
                                    jobId: job.jobId ?? "",
                                    isUnsigned: true,
                                  );
                                },
                                barrierDismissible: false,
                              );
                            }
                            bolUploadState.allowToProceed();
                          } else {
                            if (isRedundantClick(DateTime.now())) {
                              return;
                            }
                            if (createJobState.jobDropSpotUpdateLoading) {
                              return;
                            }
                            await updateJob(
                              job,
                              createJobState.selectedPickupSpot!,
                              createJobState,
                              isPickUpUpdate: true,
                            );
                            createJobState.showNewLoading();
                            await Future.delayed(const Duration(seconds: 2));
                            if (job.status == 'IN_TRANSIT') {
      print("${job.status} 1");
            print("${job.fleetStatus} 2");


                              if ((job.dropSpot == null)) {
                                showToast('Please select a drop spot');
                                return;
                              }
                              if (null == job.fleetStatus ||
                                  job.fleetStatus!.trim().isEmpty) {
                                showToast('Please select trailer status');
                                return;
                              }
                            }
                            await onConfirmTap();
                          }
                        },
                      ),
                    ),
                    gapH16,
                  ],
                ),
              ),
            ),
          );
        },
      );
    },
  );
}

showTripUpdateBottomSheetDropOff({
  required Job job,
  required bool isPickUp,
  required Function onTextChange,
  required Function onConfirmTap,
  required Function onClose,
}) {
  bool selectedOption = false;
  var controller = TextEditingController();
  showModalBottomSheet(
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    isDismissible: false,
    context: globalKey.currentContext!,
    builder: (context) {
      CreateJobState createJobState = context.watch<CreateJobState>();
      BolUploadState bolUploadState = context.watch<BolUploadState>();
      // TrailerStandUploadState trailerStandUploadState =
      //     context.watch<TrailerStandUploadState>();

      var data = MediaQuery.of(context);
      return MediaQuery(
        data: data.copyWith(
          textScaleFactor: 1,
          devicePixelRatio: 1,
        ),
        child: StatefulBuilder(
          builder:
              (BuildContext context, void Function(void Function()) setState) {
            return Container(
              // height: isDriver() && isBolEnabled() ? 536 : 466,
              height: 452,
              clipBehavior: Clip.hardEdge,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: ModalProgressHUD(
                inAsyncCall: createJobState.spotsLoading ||
                    createJobState.spotOnLocationsLoading ||
                    createJobState.truckListLoading ||
                    createJobState.driverListLoading ||
                    createJobState.createJobLoading ||
                    createJobState.entryExitLoading ||
                    createJobState.entryExitReportLoading ||
                    createJobState.jobDropSpotUpdateLoading ||
                    createJobState.newLoading,
                opacity: 0.1,
                blur: 0.3,
                color: Colors.transparent,
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  physics: const NeverScrollableScrollPhysics(),
                  child: Container(
                    padding: const EdgeInsets.all(0),
                    height: 452,
                    clipBehavior: Clip.hardEdge,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      // physics: const NeverScrollableScrollPhysics(),
                      children: [
                        Container(
                          color: appBg,
                          height: 64,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Spacer(flex: 3),
                              AppTxt(
                                text:
                                    'CONFIRM ${getHomeJobActionBtnText(job.status!)}',
                                alignment: TextAlign.center,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                              const Spacer(flex: 2),
                              IconButton(
                                onPressed: () async {
                                  onClose();
                                  closeScreen();
                                },
                                icon: const Icon(
                                  Icons.clear_outlined,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 10),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(
                              left: 20,
                              right: 20,
                            ),
                            child: ListView(
                              // crossAxisAlignment: CrossAxisAlignment.start,
                              shrinkWrap: true,
                              children: [
                                const SizedBox(height: 16),
                                const Row(
                                  children: [
                                    AppTxt(
                                      text: "Drop Off",
                                      fontSize: 14,
                                      color: appBg,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ],
                                ),
                                // const SizedBox(height: 10),
                                DropdownButton<SpotOnLocation>(
                                  hint: const AppTxt(
                                      text: 'Select Drop Location'),
                                  value: createJobState.selectedDropLocation??null,
                                  icon: createJobState.spotsLoading
                                      ? const CupertinoActivityIndicator()
                                      : const Icon(
                                          Icons.arrow_drop_down_outlined),
                                  iconSize: 24,
                                  elevation: 16,
                                  isExpanded: true,
                                  style: TextStyle(
                                      color: Theme.of(context).primaryColor),
                                  onChanged: (SpotOnLocation? spotOnLocation) {
                                    if (createJobState.spotOnLocationsLoading) {
                                      return;
                                    }
                                    print('spot------${spotOnLocation?.locationId}');
                                    createJobState.selectedDropSpot = null;
                                    job.dropLocation?.locationId =
                                        spotOnLocation?.locationId;
                                        //edit
                                    createJobState.selectedDropLocation =
                                        spotOnLocation;
                                    createJobState
                                            .jobRequestModel.dropLocationId =
                                        spotOnLocation!.locationId!;
                                    createJobState.selectedJob?.dropSpot = null;
                                    createJobState
                                            .selectedDropLocation?.locationId =
                                        spotOnLocation.locationId;
                                    createJobState.refresh();
                                    createJobState.getSpots(
                                        locationId: spotOnLocation.locationId!,
                                        drop: true);
                                  },
                                  items: createJobState.spotOnLocationList.list
                                      .map<DropdownMenuItem<SpotOnLocation>>(
                                          (SpotOnLocation value) {
                                    return DropdownMenuItem<SpotOnLocation>(
                                      value: value,
                                      child: AppTxt(
                                        text: value.locationName ?? '',
                                        fontWeight: FontWeight.bold,
                                      ),
                                    );
                                  }).toList(),
                                ),
                                gapH16,
                                const AppTxt(
                                  text: 'Select Drop Spot',
                                  fontSize: 14,
                                  color: appBg,
                                  fontWeight: FontWeight.bold,
                                ),
                                DropdownButton<Spot>(
                                  hint: const AppTxt(text: 'Select Drop Spot'),
                                  value: createJobState.selectedDropSpot,
                                  icon: createJobState.spotsLoading
                                      ? const CupertinoActivityIndicator()
                                      : const Icon(
                                          Icons.arrow_drop_down_outlined),
                                  iconSize: 24,
                                  elevation: 16,
                                  isExpanded: true,
                                  style: const TextStyle(color: appBg),
                                  onChanged: (Spot? spot) {
                                    if (createJobState.spotsLoading) {
                                      return;
                                    }

                                    setState(() {
                                      job.dropLocation?.locationId =
                                          createJobState
                                              .selectedDropLocation?.locationId;
                                      createJobState.selectedDropSpot = spot;
                                      createJobState.jobRequestModel
                                          .dropSpotId = spot!.spotId!;
                                      // job.dropSpot?.spotId = spot.spotId;

                                    job.dropSpot = PSpot(
                                      spotId: spot?.spotId,
                                      spotName: spot?.spotName,
                                      latitude: spot?.latitude,
                                      longitude: spot?.longitude,
                                      locationId: spot?.locationId,
                                      isActive: spot?.isActive ?? false,
                                      remarks: spot?.remarks,
                                      status: spot?.status,
                                      type: spot?.type,
                                    );
                                    });


                                    print('spppoooid${spot?.spotId}');
                                    print('jpppoooid${job.dropSpot?.spotId}');

                                    // showToast(
                                    //     '${job.dropSpot?.spotId}---gggggooooo----');
                                    // showToast('${spot.spotId}---ggggg----');

                                    createJobState.refresh();
                                  },
                                  items: createJobState.dropSpotsList.list
                                      .map<DropdownMenuItem<Spot>>((Spot spot) {
                                    print('sgggggg${spot.spotName}');
                                    print('sgggggg${spot.spotId}');

                                    String status = getSpotIdStatus(spot);
                                    return DropdownMenuItem<Spot>(
                                      value: spot,
                                      child: Column(
                                        children: [
                                          const SizedBox(height: 20),
                                          Row(
                                            children: [
                                              const SizedBox(width: 5),
                                              AppTxt(
                                                text: '${spot.spotName}',
                                                fontWeight: FontWeight.bold,
                                              ),
                                              const SizedBox(width: 10),
                                              const Spacer(),
                                              AppTxt(
                                                text: status,
                                                color: appBg,
                                                fontSize: 14,
                                              ),
                                              const SizedBox(width: 10),
                                            ],
                                          ),
                                        ],
                                      ),
                                    );
                                  }).toList(),
                                ),
                                gapH16,
                                Visibility(
                                  visible: !isPickUp,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const AppTxt(
                                        text: 'Select Trailer Status',
                                        alignment: TextAlign.start,
                                        fontSize: 14,
                                        color: appBg,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      DropdownButton<FleetStatus>(
                                        hint: const AppTxt(
                                          text: 'Select Trailer Status',
                                          fontSize: 12,
                                        ),
                                        value: job.fleetStatus == ''
                                            ? null
                                            : createJobState
                                                .selectedFleetStatus,
                                        icon: createJobState.truckListLoading
                                            ? const CupertinoActivityIndicator()
                                            : const Icon(
                                                Icons.arrow_drop_down_outlined),
                                        iconSize: 24,
                                        elevation: 16,
                                        isExpanded: true,
                                        style: TextStyle(
                                            color:
                                                Theme.of(context).primaryColor),
                                        onChanged: (FleetStatus? fleetStatus) {
                                          createJobState.selectedFleetStatus =
                                              fleetStatus;
                                          print('----ff--${fleetStatus?.name}');
                                          print('----ff--${fleetStatus?.id}');
                                          print('----ff--${job.fleetStatus}');
                                          job.fleetStatus = fleetStatus?.id;
                                          createJobState.jobRequestModel
                                              .fleetStatus = fleetStatus!.id;
                                          createJobState.refresh();
                                        },
                                        items: createJobState.fleetStatuses
                                            .map<DropdownMenuItem<FleetStatus>>(
                                                (FleetStatus value) {
                                          return DropdownMenuItem<FleetStatus>(
                                            value: value,
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: AppTxt(
                                                text: value.name == 'Full'
                                                    ? 'Loaded'
                                                    : value.name,
                                                fontSize: 14,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                      ),
                                    ],
                                  ),
                                ),
                                gapH4,
                                TextField(
                                  controller: controller,
                                  decoration: InputDecoration(
                                    hintText: "Drop off notes (if any)",
                                    enabledBorder: UnderlineInputBorder(
                                        borderSide:
                                            BorderSide(color: grey300!)),
                                    focusedBorder: UnderlineInputBorder(
                                        borderSide:
                                            BorderSide(color: grey300!)),
                                    hintStyle: const TextStyle(
                                        color: appBg, fontSize: 14),
                                    labelStyle: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                  onChanged: (val) {
                                    onTextChange(val);
                                  },
                                ),
                                gapH8,
                                bolUploadState.imageList.isNotEmpty
                                    ? Container(
                                        height: 80,
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 12),
                                        child: ListView.separated(
                                          scrollDirection: Axis.horizontal,
                                          itemCount:
                                              bolUploadState.imageList.length,
                                          itemBuilder: (context, index) {
                                            return Container(
                                              width: 56,
                                              height: double.infinity,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(4)),
                                              clipBehavior: Clip.hardEdge,
                                              child: Image.file(
                                                File(bolUploadState
                                                    .imageList[index].path),
                                                fit: BoxFit.cover,
                                              ),
                                            );
                                          },
                                          separatorBuilder:
                                              (BuildContext context,
                                                  int index) {
                                            return gapW8;
                                          },
                                        ),
                                      )
                                    : const SizedBox.shrink(),
                                gapH8,
                              ],
                            ),
                          ),
                        ),
                        gapH4,
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: AppBtn(
                            color: Colors.white,
                            bgColor: Theme.of(context).primaryColor,
                            text:
                                'CONFIRM ${getHomeJobActionBtnText(job.status!)}',
                            onPress: () async {
                              print('hi----${createJobState.selectedDropLocation}');
                              
                              // if(createJobState.selectedDropLocation == null) {
                              //       showToast('Please select a drop location');
                              //       return;
                              //     }edited--

                              // print(createJobState
                              //     .selectedDropLocation!.trailerStandPhoto);
                              if (!(isDriver() && isBolEnabled())) {
                                bolUploadState.allowToProceed();
                              }
                              if (!bolUploadState.proceed) {
                                if (isDriver() && isBolEnabled()) {
                                  showDialog(
                                    context: context,
                                    routeSettings:
                                        const RouteSettings(name: "popup"),
                                    builder: (context) {
                                      return AskBolDialog(
                                        jobId: job.jobId ?? "",
                                        isUnsigned: true,
                                      );
                                    },
                                    barrierDismissible: false,
                                  );
                                }
                                bolUploadState.allowToProceed();
                                // trailerStandUploadState
                                //         .trailerstandAllowToProceed();
                              } else {
                                // if (bolUploadState.proceed &&
                                //     !trailerStandUploadState.proceed) {
                                //   print("elsehj");
                                //   print(bolUploadState.proceed);
                                //   print(trailerStandUploadState.proceed);

                                //   if (isDriver() &&
                                //       createJobState
                                //               .selectedDropSpot!.spotName ==
                                //           "doc" &&
                                //       isTrailerStandEnabled()) {
                                //     showDialog(
                                //       context: context,
                                //       routeSettings:
                                //           const RouteSettings(name: "popup"),
                                //       builder: (context) {
                                //         return ConfirmDocDialog(
                                //           jobId: job.jobId ?? "",
                                //           isUnsigned: false,
                                //         );
                                //       },
                                //       barrierDismissible: false,
                                //     );
                                //     trailerStandUploadState
                                //         .trailerstandAllowToProceed();
                                //   }
                                //   if (isDriver() &&
                                //       !isTrailerStandEnabled() &&
                                //       createJobState.selectedDropLocation!
                                //               .trailerStandPhoto ==
                                //           true) {
                                //     showDialog(
                                //       context: context,
                                //       routeSettings:
                                //           const RouteSettings(name: "popup"),
                                //       builder: (context) {
                                //         return ConfirmDocDialog(
                                //           jobId: job.jobId ?? "",
                                //           isUnsigned: false,
                                //         );
                                //       },
                                //       barrierDismissible: false,
                                //     );
                                //     trailerStandUploadState
                                //         .trailerstandAllowToProceed();
                                //   }
                                // } else {
                                if (isRedundantClick(DateTime.now())) {
                                  return;
                                }
                                if (createJobState.jobDropSpotUpdateLoading) {
                                  return;
                                }
                                if (createJobState.selectedDropSpot == null) {
                                  showToast("Please select a drop spot");
                                  return;
                                }
                                print('${job.fleetStatus}-------');

                                await updateJob(
                                    job,
                                    createJobState.selectedDropSpot!,
                                    createJobState,
                                    isPickUpUpdate: false);
                                createJobState.showNewLoading();
                                await Future.delayed(
                                    const Duration(seconds: 2));
                                if (job.status == 'IN_TRANSIT') {
                                  if ((job.dropSpot == null)) {
                                    showToast('Please select a drop spot');
                                    return;
                                  }
                                  print('${job.fleetStatus}-------');

                                  if (null == job.fleetStatus ||
                                      job.fleetStatus!.trim().isEmpty) {
                                    print(
                                        '-----job-----${job.fleetStatus}-99--${job.fleetStatus!.trim().isEmpty}');
                                    showToast('Please select trailer status');
                                    return;
                                  }
                                  if (createJobState.jobDropSpotUpdateError) {
                                    return;
                                  }
                                }

                                await onConfirmTap();
                                // }
                              }
                            },
                          ),
                        ),
                        gapH24,
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      );
    },
  );
}

updateJob(Job job, Spot spot, CreateJobState createJobState,
    {bool isPickUpUpdate = false}) async {
  if (isPickUpUpdate) {
    print('pickup----1');
    ConfirmDropUpdateDropModel confirmDropUpdateDropModel =
        ConfirmDropUpdateDropModel(
      sequenceAsn: job.sequenceAsn ?? '',
      assignedToUserId: job.assignedTo!.userId!,
      description: job.description!,
      dropLocationId: job.dropLocation!.locationId!,
      dropSpotId: job.dropSpot?.spotId,
      fleetId: job.fleet!.fleetId!,
      fleetStatus: job.fleetStatus!,
      pickupLocationId: job.pickupLocation!.locationId!,
      pickupSpotId: spot.spotId!,
      priority: job.priority!,
    );
    createJobState.confirmDropUpdateDropModel = confirmDropUpdateDropModel;
    // if (job.status == 'IN_TRANSIT') {
    //   if ((createJobState.selectedDropSpot == null)) {
    //     return;
    //   }
    //   if (null == job.fleetStatus || job.fleetStatus!.trim().isEmpty) {
    //     return;
    //   }
    // }
    createJobState.doJobUpdateWithDropSpotId(
      job,
      spot,
      isPickUpUpdate: isPickUpUpdate,
    );
    return;
  }
    print('drop----1');

  ConfirmDropUpdateDropModel confirmDropUpdateDropModel =
      ConfirmDropUpdateDropModel(
    sequenceAsn: job.sequenceAsn ?? '',
    assignedToUserId: job.assignedTo!.userId!,
    description: job.description!,
    dropLocationId: job.dropLocation!.locationId!,
    dropSpotId: spot.spotId!,
    fleetId: job.fleet!.fleetId!,
    fleetStatus: job.fleetStatus!,
    pickupLocationId: job.pickupLocation!.locationId!,
    pickupSpotId: job.pickupSpot!.spotId!,
    priority: job.priority!,
  );
  createJobState.confirmDropUpdateDropModel = confirmDropUpdateDropModel;
  if (job.status == 'IN_TRANSIT') {
    if ((createJobState.selectedDropSpot == null)) {
      return;
    }
    if (null == job.fleetStatus || job.fleetStatus!.trim().isEmpty) {
      return;
    }
  }
  createJobState.doJobUpdateWithDropSpotId(
    job,
    spot,
    isPickUpUpdate: isPickUpUpdate,
  );
}

extension ToSentenceCase on String {
  String? sentenceCase() => toBeginningOfSentenceCase(toLowerCase());
}

getFormattedDateTime1(String? dateTime) {
  if (null == dateTime) {
    return '';
  }
  DateTime parseDt = DateFormat('MMM dd, yyyy HH:mm').parse(dateTime);
  String day = parseDt.day.toString().padLeft(2, '0');
  String year = parseDt.year.toString().padLeft(2, '0');
  int h = parseDt.hour;
  if (h > 12) {
    h = h - 12;
  }
  String hour = h.toString().padLeft(2, '0');
  String min = parseDt.minute.toString().padLeft(2, '0');
  String amPm = parseDt.hour > 12 ? 'PM' : 'AM';
  return '${getMonthName(parseDt.month)} $day, $year, $hour:$min $amPm';
}

getFormattedDateTime(String? dateTime) {
  if (null == dateTime) {
    return '';
  }
  DateTime parseDt = DateFormat('yyyy-MM-ddTHH:mm:ss').parse(dateTime);
  String day = parseDt.day.toString().padLeft(2, '0');
  String year = parseDt.year.toString().padLeft(2, '0');
  int h = parseDt.hour;
  if (h > 12) {
    h = h - 12;
  }
  String hour = h.toString().padLeft(2, '0');
  String min = parseDt.minute.toString().padLeft(2, '0');
  String amPm = parseDt.hour > 12 ? 'PM' : 'AM';
  return '${getMonthName(parseDt.month)} $day, $year, $hour:$min $amPm';
}

bool isTodayJob(String dateTime) {
  try {
    DateTime parseDt = DateFormat('MMM dd, yyyy').parse(dateTime);
    return isToday(parseDt);
  } catch (e) {
    return false;
  }
}

String getMonthName(int month) {
  return months[month - 1];
}

List<String> months = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec'
];

showFleetEntrySuccessBottomSheet(BuildContext context,
    {required bool isExit}) async {
  showModalBottomSheet(
    context: context,
    elevation: 6,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(16),
        topRight: Radius.circular(16),
      ),
    ),
    builder: (context) {
      return EntryExitBottomSheetContent(isExit: isExit);
    },
  );
}

Future<PackageInfo> getPackageInfo() async {
  return await PackageInfo.fromPlatform();
}

String getSpotIdStatus(Spot spot) {
  if (spot.status == 'EMPTY' && spot.fleet == null) {
    return 'Empty';
  }
  if (spot.status == 'OCCUPIED' && spot.fleet == null) {
    return 'OCCUPIED';
  }
  if (spot.status == 'TO_BE_EMPTY' && spot.fleet == null) {
    return 'Scheduled for Pick-Up';
  }
  if (spot.status == 'OCCUPIED' && spot.fleet?.fleetStatus == null) {
    return 'Occupied Empty Trailer';
  }
  if (spot.status == 'OCCUPIED' && spot.fleet?.fleetStatus == 'FULL') {
    return 'Occupied LOADED Trailer';
  }
  if (spot.status == 'OCCUPIED' && spot.fleet?.fleetStatus == 'EMPTY') {
    return 'Occupied Empty Trailer';
  }
  if (spot.status == 'TO_BE_EMPTY') {
    return 'Scheduled for Pick-Up';
  }
  if (spot.status == 'TO_BE_OCCUPIED') {
    return 'Location Reserved';
  }
  return 'Empty';
}

getJobsForHomeTab() async {
  JobsState jobsState = globalKey.currentContext!.read<JobsState>();
  await jobsState.getHomeJobs();
}

// getClientDetails() async {
//   ClientDetailsState clientDetailsState = globalKey.currentContext!.read<ClientDetailsState>();
//   await clientDetailsState.getClientDetails();
// }

bool isTrailerStandEnabled() {
  return ClientDetailsState.clientDetailsNewModelres?.trailerStandPhoto ??
      false;
}

bool isSpotter() {
  String role = Preferences.loginResponseModel!.roles.first.toLowerCase();
  return role.contains('spotter');
}

bool isDriver() {
  String role = Preferences.loginResponseModel!.roles.first.toLowerCase();
  return role.contains('driver');
}

bool isGuard() {
  String role = Preferences.loginResponseModel!.roles.first.toLowerCase();
  return role.contains('guard');
}
bool isClient() {
  String role = Preferences.loginResponseModel!.roles.first.toLowerCase();
  return role.contains('client');
}

bool isBolEnabled() {
  return BolDvirStatus.status?.bol ?? false;
}

bool isTrailerAuditEnabled() {
  return BolDvirStatus.status?.trailerAudit ?? false;
}

DateTime? loginClickTime;

bool isRedundantClick(DateTime currentTime) {
  if (loginClickTime == null) {
    loginClickTime = currentTime;
    return false;
  }
  if (currentTime.difference(loginClickTime!).inSeconds < 6) {
    return true;
  }
  loginClickTime = currentTime;
  return false;
}
