import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:spot_on/models/cliient_list_model.dart';
import 'package:spot_on/models/login/login_response_model.dart';

class Preferences {
  ///
  static final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();

  static SharedPreferences? prefs;
  static LoginResponseModel? loginResponseModel;
  static SpotOnClient? selectedClient;

  static init() async {
    prefs = await _prefs;
    String? response1 = prefs?.getString('login_info');
    String? response2 = prefs?.getString('client');
    if (null != response1) {
      loginResponseModel = loginResponseModelFromJson(response1);
    }
    if (null != response2) {
      selectedClient = SpotOnClient.fromJson(json.decode(response2));
      print("llllllll $response2");
    }
  }

  static void saveLoginInfo(LoginResponseModel loginResponseModel1) async {
    String loginJson = loginResponseModelToJson(loginResponseModel1);
    loginResponseModel = loginResponseModel1;
    prefs?.setString('login_info', loginJson);
  }

  static void removeLoginInfo() async {
    prefs?.remove('login_info');
  }

  static void removeSelectedClient() async {
    prefs?.remove('client');
  }

  static Future saveSelectedClient(SpotOnClient client) async {
    String json = jsonEncode(client);
    selectedClient = client;
    prefs?.setString('client', json);
  }

  static Future<SpotOnClient?> getSelectedClient() async {
    String? response = prefs?.getString('client');
    if (null == response) {
      return null;
    }
    SpotOnClient spotOnClient = SpotOnClient.fromJson(jsonDecode(response));
    return spotOnClient;
  }

  static bool isLoggedIn() {
    return null != loginResponseModel;
  }
}
