import 'package:flutter/material.dart';
import 'package:spot_on/screens/add_new_trailer.dart';
import 'package:spot_on/screens/client_list_screen.dart';
import 'package:spot_on/screens/create_job_screen.dart';
import 'package:spot_on/screens/dashboard.dart';
import 'package:spot_on/screens/login_screen.dart';
import 'package:spot_on/screens/login_success_screen.dart';
import 'package:spot_on/screens/reset_pwd_screen.dart';
import 'package:spot_on/screens/set_new_pwd_screen.dart';
import 'package:spot_on/utils/constants.dart';

Future openLogin() async {
  Navigator.pushReplacement(globalKey.currentContext!, MaterialPageRoute(builder: (_) => const LoginScreen()));
}

Future openHome() async {
  Navigator.pushReplacement(globalKey.currentContext!, MaterialPageRoute(builder: (_) => const DashboardScreen()));
}

Future openResetPwdScreen() async {
  Navigator.push(globalKey.currentContext!, MaterialPageRoute(builder: (_) => const ResetPwdScreen()));
}

Future openAddNewTruckScreen() async {
  Navigator.push(globalKey.currentContext!, MaterialPageRoute(builder: (_) => const AddNewTrailer()));
}

Future openSsetNewPwdScreen() async {
  Navigator.push(globalKey.currentContext!, MaterialPageRoute(builder: (_) => const SetNewPwdScreen()));
}

Future openCreateNewJobScreen() async {
  Navigator.push(globalKey.currentContext!, MaterialPageRoute(builder: (_) => const CreateJobScreen()));
}

Future openClientListScreen() async {
  Navigator.pushReplacement(globalKey.currentContext!, MaterialPageRoute(builder: (_) => const ClientListScreen()));
}

Future closeScreen() async {
  Navigator.of(globalKey.currentContext!).pop();
}

Future showLoginSuccessScreen() async {
  Navigator.pushReplacement(globalKey.currentContext!, MaterialPageRoute(builder: (_) => const LoginSuccessScreen()));
}
