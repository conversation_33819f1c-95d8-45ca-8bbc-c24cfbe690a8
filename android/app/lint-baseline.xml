<?xml version="1.0" encoding="UTF-8"?>
<issues name="AGP (7.2.1)" by="lint 7.2.1" client="gradle" dependencies="false" format="6"
    type="baseline" variant="fatal" version="7.2.1">

    <issue id="MissingDefaultResource"
        message="The drawable &quot;splash&quot; in drawable-night-hdpi has no declaration in the base `drawable` folder or in a `drawable-`*density*`dpi` folder; this can lead to crashes when the drawable is queried in a configuration that does not match this qualifier">
        <location file="src/main/res/drawable-night-hdpi/splash.png" />
    </issue>

    <issue id="MissingDefaultResource"
        message="The drawable &quot;splash&quot; in drawable-night-mdpi has no declaration in the base `drawable` folder or in a `drawable-`*density*`dpi` folder; this can lead to crashes when the drawable is queried in a configuration that does not match this qualifier">
        <location file="src/main/res/drawable-night-mdpi/splash.png" />
    </issue>

    <issue id="MissingDefaultResource"
        message="The drawable &quot;splash&quot; in drawable-night-xhdpi has no declaration in the base `drawable` folder or in a `drawable-`*density*`dpi` folder; this can lead to crashes when the drawable is queried in a configuration that does not match this qualifier">
        <location file="src/main/res/drawable-night-xhdpi/splash.png" />
    </issue>

    <issue id="MissingDefaultResource"
        message="The drawable &quot;splash&quot; in drawable-night-xxhdpi has no declaration in the base `drawable` folder or in a `drawable-`*density*`dpi` folder; this can lead to crashes when the drawable is queried in a configuration that does not match this qualifier">
        <location file="src/main/res/drawable-night-xxhdpi/splash.png" />
    </issue>

    <issue id="MissingDefaultResource"
        message="The drawable &quot;splash&quot; in drawable-night-xxxhdpi has no declaration in the base `drawable` folder or in a `drawable-`*density*`dpi` folder; this can lead to crashes when the drawable is queried in a configuration that does not match this qualifier">
        <location file="src/main/res/drawable-night-xxxhdpi/splash.png" />
    </issue>

</issues>
