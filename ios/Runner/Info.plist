<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Spot On</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>Spot On</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>FirebaseAppDelegateProxyEnabled</key>
		<true/>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>App need to pick photos from gallery to upload to server.</string>
		<key>NSCameraUsageDescription</key>
		<string>App need to take photos using camera to upload to server.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
        <string>This app requires access to your location to fetch weather data based on your current coordinates. The information displayed in the app varies according to the weather at those coordinates. As SpotOn is a fleet management app, weather conditions can significantly impact operations. </string>
        <key>NSLocationAlwaysUsageDescription</key>
        <string>This app requires access to your location to fetch weather data based on your current coordinates. The information displayed in the app varies according to the weather at those coordinates. As SpotOn is a fleet management app, weather conditions can significantly impact operations.</string>
        <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
        <string>This app requires access to your location to fetch weather data based on your current coordinates. The information displayed in the app varies according to the weather at those coordinates. As SpotOn is a fleet management app, weather conditions can significantly impact operations.</string>
        <key>LSApplicationQueriesSchemes</key>
        <array>
          <string>https</string>
        </array>
	</dict>
</plist>
