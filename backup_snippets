 // FutureBuilder(
                          //   future: Preferences.getSelectedClient(),
                          //   builder: (context, snapshot) {
                          //     if (!snapshot.hasData) {
                          //       return const AppTxt(text: 'Loading...');
                          //     }
                          //     SpotOnClient spotOnClient =
                          //         snapshot.data as SpotOnClient;
                          //     return AppTxt(text: '${spotOnClient.clientName}');
                          //   },
                          // ),