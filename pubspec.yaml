name: spot_on
description: A new Flutter project.

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.6+14

environment:
  sdk: ">=2.16.1 <4.0.0"

dependencies:
  autocomplete_textfield: ^2.0.1
  awesome_notifications: ^0.8.2
  cached_network_image: ^3.2.1
  cupertino_icons: ^1.0.2
  dio: ^5.3.3
  dio_smart_retry: ^6.0.0
  easy_image_viewer: ^1.2.1
  email_validator: ^2.0.1
  equatable: ^2.0.5
  firebase_core: ^2.9.0
  firebase_messaging: ^14.4.0
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.3
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.2.19
  flutter_typeahead: ^4.3.7
  fluttertoast: ^8.2.1
  http: ^0.13.5
  http_interceptor: ^1.0.2
  image_picker: ^1.0.4
  intl: ^0.18.0
  location: ^5.0.3
  material_color_gen: ^2.0.0
  modal_progress_hud_nsn: ^0.4.0
  package_info: ^2.0.2
  package_info_plus: ^3.0.3
  package_info_plus_web: ^2.0.0
  platform_device_id: ^1.0.1
  provider: ^6.0.0
  shared_preferences: ^2.0.13
  status_change: ^2.0.0
  url_launcher: ^6.3.0
  webview_flutter: ^4.5.0

dev_dependencies:

  flutter_lints: ^2.0.1
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true

  assets:
    - assets/images/

  fonts:
    - family: Bronova
      fonts:
        - asset: assets/fonts/Bronova_Regular.otf

flutter_native_splash:
  color: "#56FFB5"
  image: assets/images/spot_on_logo_splash.png
  color_dark: "#56FFB5"
  image_dark: assets/images/spot_on_logo_splash.png
  android_12:
    image: assets/images/spot_on_logo_splash.png
    color: "#56FFB5"
    image_dark: assets/images/spot_on_logo_splash.png
    color_dark: "#56FFB5"

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/spot_on_logo.png"
  min_sdk_android: 21
  adaptive_icon_background: "assets/images/spot_on_logo.png"
  adaptive_icon_foreground: #56FFB5
    null
  remove_alpha_ios: true
